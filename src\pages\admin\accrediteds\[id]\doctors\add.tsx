import {
  VStack,
  useToast,
  Box,
  Flex,
  HStack,
  SimpleGrid,
} from "@chakra-ui/react";
import { NextPage } from "next";

import * as yup from "yup";
import { SubmitHandler, useForm } from "react-hook-form";

import { AxiosError, AxiosResponse } from "axios";
import { useMutation } from "@tanstack/react-query";
import { yupResolver } from "@hookform/resolvers/yup";

import { api } from "~/services/apiClient";
import { Option } from "~/utils/Types/Global";
import { queryClient } from "~/services/queryClient";
import { UserFormData } from "~/utils/Types/Admin/User";

import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit";
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit";
import { useMemo } from "react";
import { useAccreditedsAdmin } from "~/hooks/Admin/Accrediteds/useAccreditedsAdmin";
import { useControlFilters } from "~/contexts/ControlFiltersContext";

import { InputSearchSelect } from "~/components/global/Form/inputSearchSelect";
import { useRouter } from "next/router";
type DoctorFormData = UserFormData & {
  doctor: Option;
};

const FormSchema = yup.object().shape({
  doctor: yup.object().required("Nome obrigatório"),
});

interface DoctorsAddProps {}

const DoctorsAdd: NextPage<DoctorsAddProps> = ({}) => {
  const toast = useToast();

  const {
    formState,
    formState: { errors },
    handleSubmit,
    control,
  } = useForm<DoctorFormData>({
    //@ts-ignore
    resolver: yupResolver(FormSchema),
    defaultValues: {},
  });
  const { page, limit, search, setSearch } = useControlFilters();

  const { data, isLoading } = useAccreditedsAdmin({
    page,
    search,
    limit,
  });

  const router = useRouter()

  const accreditedsOptions = useMemo(() => {
    const accrediteds = data?.accrediteds.filter(
      (accredited) => accredited.type === "doctor"
    );
    const options: Option[] = [];
    accrediteds?.forEach((accredited) =>
      options.push({ label: accredited.name, value: accredited.secure_id })
    );
    return options;
  }, [data]);

  const loadOptions = (inputValue: string) => {
    setSearch(inputValue);
  };

  const add = useMutation(
    async (values: DoctorFormData) => {
      return await api.post(`/v1/admin/accrediteds/${router.query.id}/doctors`, {
        doctor_id: values.doctor.value,
      });
    },
    {
      onSuccess: (response: AxiosResponse) => {
        queryClient.invalidateQueries(["ClinicsAdmin"]);
        toast({
          title:
            response.data?.message || "Novo médico adicionado com sucesso!",
          position: "top-right",
          status: response.data?.type || "success",
          isClosable: true,
        });
        history.back();
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title:
            error?.response?.data?.message ||
            "Ocorreu um problema ao adicionar médico.",
          position: "top-right",
          status: error?.response?.data?.type || "error",
          isClosable: true,
        });
      },
    }
  );

  const handleAdd: SubmitHandler<DoctorFormData> = async (values) => {
    try {
      await add.mutateAsync(values);
    } catch {}
  };

  return (
    <VStack spacing="4" layerStyle="container">
      <Box
        p="4"
        as="form"
        width="100%"
        layerStyle="card"
        onSubmit={handleSubmit(handleAdd)}
      >
        <VStack spacing="4" align="center">
          <SimpleGrid w="100%" spacing={6} minChildWidth="180px" maxW="500px">
            <InputSearchSelect
              label="Nome do médico"
              placeholder="Nome do médico"
              options={accreditedsOptions}
              control={control}
              isLoading={isLoading}
              onInputChange={(e) => loadOptions(e)}
              name="doctor"
              error={errors.doctor as any}
            />
          </SimpleGrid>

          <Flex justify="center" w="100%">
            <HStack spacing="4" width="20em">
              <ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
              <ButtonSubmit isLoading={formState.isSubmitting}>
                Adicionar
              </ButtonSubmit>
            </HStack>
          </Flex>
        </VStack>
      </Box>
    </VStack>
  );
};

export default DoctorsAdd;
