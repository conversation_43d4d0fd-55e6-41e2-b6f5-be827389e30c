import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  Select as ChakraSelect,
  SelectProps as ChakraSelectProps,
  Text,
} from "@chakra-ui/react";
import { FieldError } from "react-hook-form";

import { forwardRef, ForwardRefRenderFunction } from "react";
import { Option } from "~/utils/Types/Global";

interface InputProps extends ChakraSelectProps {
  name: string;
  label?: string;
  placeholder?: string;
  error?: FieldError;
  options: Option[];
  onSelectElement?: (e: string) => void;
}

const InputBase: ForwardRefRenderFunction<HTMLSelectElement, InputProps> = (
  { name, label, options, onSelectElement, error = null, ...rest },
  ref
) => {
  return (
    <FormControl
      isInvalid={!!error}
      w="inherit"
      width="100%"
      onChange={(e: any) => onSelectElement && onSelectElement(e.target.value)}
    >
      {!!label && (
        <FormLabel
          w="100%"
          htmlFor={name}
          display="flex"
          alignItems="center"
          fontWeight="bold"
        >
          {label}
        </FormLabel>
      )}
      <ChakraSelect
        fontSize={{ sm: "md", md: "lg" }}
        id={name}
        name={name}
        variant="outline"
        size="lg"
        ref={ref}
        {...rest}
      >
        {options.map((option) => (
          <Text
            as="option"
            key={option.value}
            value={option.value}
            // color="gray.500"
          >
            {option.label}
          </Text>
        ))}
      </ChakraSelect>
      {error && (
        <FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
      )}
    </FormControl>
  );
};

export const InputSelect = forwardRef(InputBase);
