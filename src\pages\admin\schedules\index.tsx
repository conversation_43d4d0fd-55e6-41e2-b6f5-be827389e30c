import {
	Box,
	Flex,
	<PERSON>rid,
	<PERSON><PERSON><PERSON><PERSON>,
	Spinner,
	<PERSON>,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useScheduleAdmin } from "~/hooks/Admin/Schedule/useScheduleAdmin"
import { useCan } from "~/hooks/useCan"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"


import { CardScheduleAdmin } from "~/components/Admin/Schedule/CardScheduleAdmin"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { Pagination } from "~/components/global/Pagination"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { ButtonSortTable } from "~/components/global/Buttons/ButtonSortTable"
import { FormatDateAsHour } from "~/utils/Functions/FormatDates"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"
import { useEffect, useState } from "react"

interface ScheduleProps {

}

const Schedule: NextPage<ScheduleProps> = () => {
	const { page, limit, search, setPage, setLimit, setSearch, direction, field, setField, setDirection, fieldSelect, setFieldSelect } = useControlFilters()
	
	const [currentStatus, setCurrentStatus] = useState<string>("all")

	const { data, isLoading, error, isFetching } = useScheduleAdmin({
		page,
		search,
		limit,
		direction,
		field,
		status: fieldSelect,
		currentStatus
	})

	const userCanSeeEdit = useCan({
		permissions: ['schedule_edit']
	})

	const userCanSeeCreate = useCan({
		permissions: ['schedule_delete']
	})

	useEffect(() => {
		setFieldSelect('all');
	}, []);

	return (
		<VStack spacing="4" layerStyle="container">
			<HStack w="100%" justify="space-between" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Solicitações de Agendamentos
				</Text>
				{isFetching && !isLoading && (
					<Spinner />
				)}
				{userCanSeeCreate && (
					<ButtonToCreate linkHref="/admin/schedules/add">
						Nova
					</ButtonToCreate>
				)}
			</HStack>
			<VStack layerStyle="card" width="100%" p="4">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
					<Flex justify="space-between" align="center" w="100%">
						<HStack gap='3'>
							<InputSelect
									width='12em'
									name="currentStatus"
									options={[
										{ label: 'Todas', value: 'all' },
										{ label: 'Aberta', value: 'open' },
										{ label: 'Encerrada', value: 'closed' },
									]}
									onChange={(event) => setCurrentStatus(event.target.value)}
									bg="blackAlpha.100"
								/>
								<InputSelect
									width='20em'
									name="status"
									options={[
										{ label: 'Todas', value: 'all' },
										{ label: 'Agenda', value: 'in_schedule' },
										{ label: 'Aguardando Backoffice Pontual', value: 'waiting_backoffice' },
										{ label: 'Aguardando Backoffice em Rede', value: 'waiting_backoffice_network' },
										{ label: "Orçamento", value: "budget" },
										{ label: 'Aguardando Paciente', value: 'waiting_patient' },
										// { label: 'Aprovada', value: 'approved' },
										{ label: 'Cancelado pelo Paciente', value: 'canceled_by_patient' },
										{ label: 'Cancelado a pedido do Paciente', value: 'canceled_at_patient_request' },
										{ label: 'Cancelado pelo Backoffice', value: 'canceled_by_backoffice' },
										{ label: 'Em credenciamento', value: 'in_accreditation' },
										{ label: 'Sem Contato', value: 'no_contact' },
										{ label: 'Sem Interesse', value: 'no_interest' },
										{ label: 'Falta Pedido', value: 'lack_request' },
										{ label: 'Divergência de Informações', value: 'info_divergence' },
										{ label: 'Condição Financeira', value: 'financial_condition' },
										{ label: 'Sem Interesse Credenciamento', value: 'no_interest_accreditation' },
									]}
									onChange={event => setFieldSelect(event.target.value)}
									bg="blackAlpha.100"
								/>
						</HStack>
						<HStack spacing="4" align="center">
							{isFetching && !isLoading && (
								<Spinner />
							)}
							<Box w="72">
								<InputSearch
									name="search"
									placeholder="Nome ou CPF"
									setPage={setPage}
									setSearch={setSearch}
								/>
							</Box>
						</HStack>

					</Flex>
				</Flex>
				{data && (
					<>
						<TableContainer w="100%">
							<Table whiteSpace="pre-wrap">
								<Thead>
									<Tr>
										<Th>Paciente</Th>
										{/* <Th>Tipo</Th> */}
										<Th>Cidade</Th>
										<Th>Especialidade/Exame</Th>
										<Th>
											<Flex justify="center">
												<ButtonSortTable
													label="Data da solicitação"
													field="created_at"
													direction={direction}
													setDirection={setDirection}
													setField={setField}
													currentField={field}
												/>
											</Flex>
										</Th>
										<Th justifyContent="center">
											Data Retorno
											{/* <Flex justify="center">
												<ButtonSortTable
													label="Data Retorno"
													field="recurrence_date"
													direction={direction}
													setDirection={setDirection}
													setField={setField}
													currentField={field}
												/>
											</Flex> */}
										</Th>

										
									<Th colSpan={3} bg="white" right='0' position='sticky'>
									<Grid templateColumns="7rem 8rem 5rem" gap='6' alignItems="center">
										{/* <Th justifyContent="center" position="sticky" right="17rem" bg="white" zIndex="1"> */}
											<Flex justify="center">
												<ButtonSortTable
													label="Status Atual"
													field="status"
													direction={direction}
													setDirection={setDirection}
													setField={setField}
													currentField={field}
												/>
											</Flex>
										{/* </Th> */}
										{/* <Th justifyContent="center" position="sticky" right="6rem" bg="white" zIndex="1"> */}
											<Flex justify="center">
												<ButtonSortTable
													label="Sub-Status"
													field="status"
													direction={direction}
													setDirection={setDirection}
													setField={setField}
													currentField={field}
												/>
											</Flex>
										{/* </Th> */}
										{userCanSeeEdit && (
											// <Th position="sticky" right="0" bg="white" zIndex="1">
												<Text
													align="center"
												>
													Ações
												</Text>
											// </Th>
										)}
										</Grid>
										</Th>
									</Tr>
								</Thead>
								<Tbody>
									{data.schedules.map(schedule => (
										<CardScheduleAdmin
											key={schedule.secure_id}
											secure_id={schedule.secure_id}
											patient={schedule.patient.userInfo.name}
											type={schedule.type_consult}
											specialtyOrExam={schedule.specialty ? schedule.specialty.name : schedule.exam!.name}
											currentStatus={schedule.current_status}
											status={schedule.status}
											createdAt={schedule.created_at}
											followUpDate={schedule.followUpDate}
											city={schedule.city}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.schedules.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['schedule_view']
})

export default Schedule
