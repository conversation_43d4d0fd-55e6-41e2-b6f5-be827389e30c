import {
	Box,
	Flex,
	<PERSON><PERSON><PERSON><PERSON>,
	Spinner,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import { useCan } from "~/hooks/useCan"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useExamsAdmin } from "~/hooks/Admin/Exams/useSpecialtiesAdmin"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"
import { CardExamAdmin } from "~/components/Admin/Exams/CardExamAdmin"


interface ExamsProps {

}

const Exams: NextPage<ExamsProps> = () => {
	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters()

	const { data, isLoading, error, isFetching } = useExamsAdmin({ page, search, limit })

	const userCanSeeCreate = useCan({
		permissions: ['exams_create']
	})

	const userCanSeeEdit = useCan({
		permissions: ['exams_edit']
	})

	return (
		<VStack spacing="4" layerStyle="container">
			<Flex w="100%" justify="space-between" align="center">
				<Text
					textStyle="headerLG"
					as="header"
				>
					Exames
				</Text>
				{userCanSeeCreate && (
					<ButtonToCreate linkHref="/admin/exams/add">
						Nova
					</ButtonToCreate>
				)}
			</Flex>
			<VStack layerStyle="card" width="100%" p="4">
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
					<HStack spacing="4" align="center">
						{isFetching && !isLoading && (
							<Spinner />
						)}
						<Box w="72">
							<InputSearch
								name="search"
								placeholder="Nome"
								setPage={setPage}
								setSearch={setSearch}
							/>
						</Box>
					</HStack>
				</Flex>
				{data && (
					<>
						<TableContainer w="100%">
							<Table>
								<Thead>
									<Tr>
										<Th>Nome</Th>
										<Th>
											<Text
												align="center"
											>
												Status
											</Text>
										</Th>
										{userCanSeeEdit && (
											<Th>
												<Text
													align="center"
												>
													Ações
												</Text>
											</Th>
										)}
									</Tr>
								</Thead>
								<Tbody>
									{data.exams.map(exams => (
										<CardExamAdmin
											key={exams.secure_id}
											secure_id={exams.secure_id}
											name={exams.name}
											status={exams.active}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.exams.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['exams_view']
})

export default Exams
