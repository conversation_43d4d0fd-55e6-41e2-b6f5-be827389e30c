import { Button, ButtonProps, Icon } from "@chakra-ui/react"
import Link from "next/link"
import { FC, ReactNode } from "react"
import { RiAddLine } from "react-icons/ri"

interface ButtonToCreateProps extends ButtonProps {
	children: ReactNode
	linkHref: string
}

export const ButtonToCreate: FC<ButtonToCreateProps> = ({ children, linkHref }) => {
	return (
		<Button
			as={Link}
			href={linkHref}
			passHref
			size="md"
			fontSize="md"
			colorScheme="green"
			leftIcon={<Icon as={RiAddLine} fontSize="20" />}
		>
			{children}
		</Button>
	)
}
