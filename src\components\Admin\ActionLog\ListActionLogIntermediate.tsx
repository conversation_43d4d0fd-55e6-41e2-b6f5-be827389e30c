import { Box, BoxProps, Flex, Table, TableContainer, Tbody, Td, Th, Thead, Tr } from "@chakra-ui/react"
import { FC } from "react"

import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useActionLogsAdmin } from "~/hooks/Admin/ActionLogs/useActionLogsAdmin"
import { FormatDateForYearMonthDayHoursSecondsUsingBars } from "~/utils/Functions/FormatDates"

import { Pagination } from "~/components/global/Pagination"

interface ListActionLogIntermediateProps extends BoxProps {
	changedSecureId: string
	type: 'user' | 'specialty' | 'exam' | 'appointment' | 'schedule' | 'group'
}

export const ListActionLogIntermediate: FC<ListActionLogIntermediateProps> = ({ changedSecureId, type, ...rest }) => {
	const { page, limit, setPage, setLimit } = useControlFilters()

	const { data } = useActionLogsAdmin({ page, limit, changedSecureId, type })

	return (
		<Box w="100%" p="4" {...rest}>
			<TableContainer w="100%">
				<Table>
					<Thead>
						<Tr>
							<Th>Data</Th>
							<Th>Usuário que alterou</Th>
							<Th>Dados alterados</Th>
						</Tr>
					</Thead>
					<Tbody>
						{data && data.logs.map(log => (
							<Tr key={`${log.user.secure_id}_${log.charged_data}`}>
								<Td>{FormatDateForYearMonthDayHoursSecondsUsingBars(log.date)}</Td>
								<Td>{log.user.userInfo.name}</Td>
								<Td whiteSpace="pre-line">{log.charged_data.replace(/;/g, "\n")}</Td>
							</Tr>
						))}
					</Tbody>
				</Table>
			</TableContainer>
			{data && (
				<Flex justify="flex-end" w="100%">
					<Pagination
						totalCountOfRegisters={data.total}
						registersInCurrentPage={data.logs.length}
						currentPage={data.page}
						registersPerPage={data.perPage}
						onPageChange={setPage}
						limit={limit}
						setLimit={setLimit}
					/>
				</Flex>
			)}
		</Box>
	)
}
