import { useQuery } from "@tanstack/react-query";
import { api } from "~/services/apiClient";
import { ListAppointmentDoctor } from "~/utils/Types/Doctor/Appointments";

type GetAppointmentResponse = {
  total: number;
  page: number;
  lastPage: number;
  perPage: number;
  appointments: ListAppointmentDoctor[];
};

type GetAppointmentProps = {
  page: number;
  search?: string;
  limit?: number;
};

export async function getAppointmentDoctor({
  page,
  search,
  limit,
}: GetAppointmentProps): Promise<GetAppointmentResponse> {
  const response = await api.get("/v1/admin/appointments-doctors", {
    params: {
      search,
      page,
      limit,
      typeConsult: "video_call"
    },
  });

  return {
    total: response.data.meta.total,
    perPage: response.data.meta.per_page,
    page: response.data.meta.current_page,
    lastPage: response.data.meta.last_page,
    appointments: response.data.data,
  };
}

export function useAppointmentDoctor({
  page,
  search,
  limit,
}: GetAppointmentProps) {
  return useQuery(["AppointmentDoctor", page, search, limit], () =>
    getAppointmentDoctor({ page, search, limit })
  );
}
