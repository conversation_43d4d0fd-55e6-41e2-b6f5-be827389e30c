import { <PERSON><PERSON>, <PERSON>rid, <PERSON>rid<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalOverlay, Stack, useToast } from "@chakra-ui/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { useCallback, useEffect, useMemo } from "react";
import { useForm } from "react-hook-form";
import { InputDate } from "~/components/global/Form/InputDate";
import { InputNumberMask } from "~/components/global/Form/InputNumberMask";
import { InputSelect } from "~/components/global/Form/InputSelect";
import { useShowSchedulePaymentData } from "~/hooks/Admin/Schedule/useShowSchedulePaymentData";
import { api } from "~/services/apiClient";
import { queryClient } from "~/services/queryClient";
import * as yup from "yup"
import { yupResolver } from "@hookform/resolvers/yup";
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect";

type OptionsSelectProps = {
	label: string;
	value: string | number;
}

type PartnersProps = {
	secure_id: string;
	userInfo: {
		name: string;
	};
}[];

type FormDataProps = {
	scheduleSecureId: string;
	partnerSecureId: {
		label: string,
		value: string,
	};
	paymentMethods: string;
	queryValue: string;
	queryValueSubsidy: string;
	queryValuePatient: string;
	date: string;
}

type ModalAddDisponibilityProps = {
	isOpen: boolean;
	onClose: () => void;
	typeConsult?: "in_person" | "video_call" | "exam";
	scheduleSecureId: string;
}

const FormSchema = yup.object().shape({
	scheduleSecureId: yup.string(),
	paymentMethods: yup.string(),
	partnerSecureId: yup.object().shape({
		value: yup.string().required(),
		label: yup.string().required(),
	}).required("Médico obrigatório"),
	date: yup.string().required("Data obrigatória"),
	queryValue: yup.string().required("Valor obrigatório").test(
		'is-greater-than-subsidy',
		'Valor da consulta deve ser maior que o valor de subsídio', 
		(value, ctx) => Number(ctx.parent.queryValue) > Number(ctx.parent.queryValueSubsidy)
	),
	queryValueSubsidy: yup.string().required("Valor obrigatório"),
	queryValuePatient: yup.string().required("Valor obrigatório"),
})

export function ModalAddDisponibility({ isOpen, onClose, typeConsult, scheduleSecureId }: ModalAddDisponibilityProps) {
	const { formState: { errors }, handleSubmit, register, setValue, reset, watch, control } = useForm<FormDataProps>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
	});

	const toast = useToast()
	const partnerSecureId = watch('partnerSecureId')
	const queryValue = watch('queryValue')
	const queryValueSubsidy = watch('queryValueSubsidy')

	const { data: partnersData } = useQuery(["accrediteds", typeConsult], async () => {
		const response = await api.get(`/v1/admin/accrediteds`, {
			params: {
				limit: 9999,
				type: typeConsult === "exam" ? "lab" : "doctor",
			},
		});

		return response.data.data as PartnersProps;
	});

	const { data: schedulePaymentData } = useShowSchedulePaymentData({
		partnerId: partnerSecureId?.value,
	});

	const definePatientValue = () => {
		if (queryValueSubsidy === '') return queryValue
		const patientValue = Number(queryValue) - Number(queryValueSubsidy)
		return patientValue.toFixed(2)
	}

	useEffect(() => {
		if (schedulePaymentData?.paymentInfo.query_value) {
			setValue('queryValue', (schedulePaymentData?.paymentInfo.query_value / 100).toString() || "");
		}
		setValue('queryValueSubsidy', "");
		setValue('queryValuePatient', definePatientValue());
	}, [schedulePaymentData])

	const handleNewDate = useMutation(async (data: FormDataProps) => {
		return api.post("/v1/admin/schedules-create-dates", {
			...data,
			partnerSecureId: partnerSecureId.value,
			queryValue: Number(data.queryValue) * 100,
			queryValueSubsidy: Number(data.queryValueSubsidy) * 100,
			queryValuePatient: Number(data.queryValuePatient) * 100,
			scheduleSecureId,
		});
	},
		{
			onSuccess: () => {
				queryClient.invalidateQueries(["schedules"]);
				queryClient.invalidateQueries(["ScheduleAdmin"])
				toast({
					title: "Horário adicionado com sucesso.",
					position: "top-right",
					status: "success",
					isClosable: true,
				});
				onClose();
				reset();
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message || "Erro ao adicionar o horário.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			},
		}
	);


	const handleAdd = async (data: FormDataProps) => {
		try {
			await handleNewDate.mutateAsync(data);
		} catch { }
	};

	const handleGetSearchAccrediteds = useCallback(async (inputValue: string): Promise<OptionsSelectProps[]> => {
		try {
			const response = await api.get('/v1/admin/accrediteds', {
				params: {
					search: inputValue,
					page: 1,
					limit: 20,
					// type: typeConsult === "exam" ? "lab" : "doctor" || "clinic",
					field: "name"
				}
			})

			const search = response.data.data.map(({ secure_id, userInfo }: any): OptionsSelectProps => ({
				label: userInfo.name,
				value: secure_id,
			}))

			return search
		} catch {
			return []
		}
	}, [])

	const { data: accreditedsData } = useQuery(["accrediteds", typeConsult], async () => {
		const response = await api.get(`/v1/admin/accrediteds`, {
			params: {
				limit: 10,
				type: 'all'
			},
		});

		const search = response.data.data.map(({ secure_id, userInfo }: any): OptionsSelectProps => ({
			label: userInfo.name,
			value: secure_id,
		}))

		return search;
	});

	const optionsPartiners = useMemo(() => {
		if (!accreditedsData?.length) return []

		return accreditedsData?.map((item: any) => ({
			label: item.userInfo.name,
			value: item.secure_id,
		}))
	}, [accreditedsData])

	const handleModalClose = () => {
    resetSpecificFields(); 
    onClose();
  };

	const resetSpecificFields = () => {
    setValue('queryValue', '');
    setValue('queryValueSubsidy', '');
  };

	return (
		<Modal isOpen={isOpen} onClose={handleModalClose}>
			<ModalOverlay />
			<ModalContent
				as="form"
				onSubmit={handleSubmit(handleAdd)}
			>
				<ModalHeader>Novo Horário</ModalHeader>
				<ModalBody>
					<Stack backgroundColor="white">
						<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
							{/* <GridItem colSpan={12}>
								<InputAsyncSelect
									isMulti={false}
									isClearable
									control={control}
									name="partnerSecureId"
									error={errors.partnerSecureId}
									label={typeConsult === "exam" ? "Laboratório" : "Parceiro"}
									placeholder={typeConsult === "exam" ? "Procure um Laboratório" : "Procure um Parceiro"}
									handleSearch={typeConsult === "exam" ? handleSearchExams : handleSearchSpecialties}
								/>
							</GridItem> */}
							<GridItem colSpan={12}>
								<InputAsyncSelect
									isMulti={false}
									isClearable
									control={control}
									name="partnerSecureId"
									error={errors.partnerSecureId}
									label={typeConsult === "exam" ? "Laboratório" : "Credenciado"}
									placeholder={typeConsult === "exam" ? "Procure um Laboratório" : "Procure um Credenciado"}
									handleSearch={handleGetSearchAccrediteds}
									loadOptions={handleGetSearchAccrediteds}
									defaultOptions={optionsPartiners}
									cacheOptions={false}
								/>
							</GridItem>
							<GridItem colSpan={12}>
								<InputDate
									label="Data"
									type="datetime-local"
									{...register("date")}
									error={errors.date}
								/>
							</GridItem>
							<GridItem colSpan={12}>
								<InputNumberMask
									setValue={setValue}
									placeholder="R$ 0,00"
									prefix="R$ "
									error={errors.queryValue}
									label="Valor Hellomed"
									name="queryValue"
								/>
							</GridItem>
							<GridItem colSpan={12}>
								<InputNumberMask
									setValue={setValue}
									placeholder="R$ 0,00"
									prefix="R$ "
									error={errors.queryValueSubsidy}
									label="Valor Subsidiado"
									name="queryValueSubsidy"
									value={''}
								/>
							</GridItem>
							<GridItem colSpan={12}>
								<InputNumberMask
									isReadOnly
									setValue={setValue}
									placeholder="R$ 0,00"
									prefix="R$ "
									error={errors.queryValuePatient}
									label="Valor Paciente"
									name="queryValuePatient"
									cursor='not-allowed'
									value={definePatientValue()}
								/>
							</GridItem>
						</Grid>
					</Stack>
				</ModalBody>

				<ModalFooter>
					<Button variant="ghost" onClick={handleModalClose}>
						Cancelar
					</Button>
					<Button colorScheme="blue" type="submit" mr={3}>
						Adicionar
					</Button>
				</ModalFooter>
			</ModalContent>
		</Modal>
	)
}
