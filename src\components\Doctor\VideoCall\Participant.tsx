import { Flex, FlexProps } from "@chakra-ui/react";
import React, { useEffect, useRef, useState } from "react";
import {
  Participant as ParticipantTwilioProps,
  VideoTrack,
  AudioTrack,
} from "twilio-video";
interface ParticipantProps extends FlexProps{
  participant?: ParticipantTwilioProps;
  isLoading?: boolean;
  rounded?: string;
}

export default function Participant({
  participant,
  isLoading,
  rounded,
}: ParticipantProps) {
  const [videoTracks, setVideoTracks] = useState<VideoTrack[]>([]);
  const [audioTracks, setAudioTracks] = useState<AudioTrack[]>([]);

  const videoRef: any = useRef<HTMLVideoElement>();
  const audioRef: any = useRef<HTMLAudioElement>();

  const trackpubsToTracks = (trackMap: any) =>
    Array.from(trackMap?.values())
      .map((publication: any) => publication.track)
      .filter((track) => track !== null);

  useEffect(() => {
    if (!isLoading && !!participant) {
      const trackSubscribed = (track: VideoTrack | AudioTrack) => {
        if (track.kind === "video") {
          setVideoTracks((videoTracks) => [...videoTracks, track]);
        } else {
          setAudioTracks((audioTracks) => [...audioTracks, track]);
        }
      };

      const trackUnsubscribed = (track: VideoTrack | AudioTrack) => {
        if (track.kind === "video") {
          setVideoTracks((videoTracks) =>
            videoTracks.filter((v) => v !== track)
          );
        } else {
          setAudioTracks((audioTracks) =>
            audioTracks.filter((a) => a !== track)
          );
        }
      };

      if (participant?.videoTracks) {
        setVideoTracks(trackpubsToTracks(participant?.videoTracks));
      }
      if (participant?.audioTracks) {
        setAudioTracks(trackpubsToTracks(participant?.audioTracks));
      }

      participant.on("trackSubscribed", trackSubscribed);
      participant.on("trackUnsubscribed", trackUnsubscribed);

      return () => {
        setVideoTracks([]);
        setAudioTracks([]);
        participant.removeAllListeners();
      };
    }
  }, [participant, isLoading]);

  useEffect(() => {
    const videoTrack = videoTracks[0];
    if (videoTrack) {
      videoTrack.attach(videoRef.current);
      return () => {
        videoTrack.detach();
      };
    }
  }, [videoTracks]);

  useEffect(() => {
    const audioTrack = audioTracks[0];
    if (audioTrack) {
      audioTrack.attach(audioRef.current);
      return () => {
        audioTrack.detach();
      };
    }
  }, [audioTracks]);
  return (
    <>
      <Flex
        ref={videoRef}
        as="video"
        minWidth="100%"
        height="100%"
        autoPlay={true}
        objectFit="cover"
        rounded={rounded}
      />
      <Flex ref={audioRef} as="audio" autoPlay={true} muted={false} />
    </>
  );
}
