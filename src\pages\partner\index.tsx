import { GetServerSideProps, NextPage } from "next";

import { useAuthContext } from "~/contexts/AuthContext";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";

import { ModalChangePassword } from "~/components/global/ModalChangePassword";

interface PartnerProps {}

const Partner: NextPage<PartnerProps> = () => {
	const { user } = useAuthContext()

	return (
		<>
			<div />
			{user && user.isFirstAccess && (
				<ModalChangePassword
					isOpen={user.isFirstAccess}
				/>
			)}
		</>
	)
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		return {
			props: {},
		};
	},
	{
		roles: ["PARTNER"],
	}
);

export default Partner;
