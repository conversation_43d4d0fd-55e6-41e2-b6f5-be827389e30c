import { GetServerSideProps, NextPage } from "next"

import { useAuthContext } from "~/contexts/AuthContext"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { DashboardPanel } from "~/components/Admin/Dashboard/DashboardPanel"
import { ModalChangePassword } from "~/components/global/ModalChangePassword"
interface AdminProps {

}

const Admin: NextPage<AdminProps> = () => {
	const { user } = useAuthContext()

	return (
		<>
			<DashboardPanel />
			{user && user.isFirstAccess && (
				<ModalChangePassword
					isOpen={user.isFirstAccess}
				/>
			)}
		</>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
})

export default Admin
