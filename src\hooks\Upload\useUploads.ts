import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
// import { ListFileAdmin } from "~/utils/Types/Admin/Files"

type GetUploadsResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	uploads: any[]
}

type GetUploadsProps = {
	page: number
	limit?: number
	type?: 'image' | 'pdf'
	search?: string
}

export async function getUploads({ page, limit, type, search }: GetUploadsProps): Promise<GetUploadsResponse> {
	const response = await api.get('/v1/uploads', {
		params: {
			page,
			limit: limit || 15,
			type,
			search,
		}
	})

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		uploads: response.data.data
	}
}

export function useUploads({ page, limit, type, search }: GetUploadsProps) {
	return useQuery(['Uploads', page, limit, type, search], () => getUploads({ page, limit, type, search }))
}
