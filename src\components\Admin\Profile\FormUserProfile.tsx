import {
	Box,
	Divider,
	Grid,
	GridItem,
	Text,
} from '@chakra-ui/react'
import { FC, useEffect } from 'react'
import { FormState, UseFormClearErrors, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form'

import { api } from '~/services/apiClient'
import { ShowUser, UserFormData } from '~/utils/Types/Admin/User'
import { useDebouncedPromise } from '~/utils/Functions/useDebouncedPromise'
import { Input } from '~/components/global/Form/Input'
import { InputImage } from '~/components/global/Form/ImputImage'
import { InputMask } from '~/components/global/Form/InputMask'
import { InputDate } from '~/components/global/Form/InputDate'
import { InputPassword } from '~/components/global/Form/InputPassword'

interface FormUserProps {
	watch: UseFormWatch<UserFormData>
	formState: FormState<UserFormData>
	register: UseFormRegister<UserFormData>
	setValue: UseFormSetValue<UserFormData>
	clearErrors: UseFormClearErrors<UserFormData>
	isEditForm?: boolean
	textUserExists: string
}

export const FormUserProfile: FC<FormUserProps> = ({ register, clearErrors, setValue, watch, formState: { errors }, isEditForm = false, textUserExists }) => {
	const name = watch('name')
	// const email = watch('email')
	const zipCode = watch('zip_code')
	const userExists = watch('userExists')

	const getCep = async (value: string) => {
		if (value && value.length === 8) {
			api.get(`https://brasilapi.com.br/api/cep/v2/${value}`).then(response => {
				const data: any = response.data;
				setValue('street', data.street)
				setValue('city', data.city)
				setValue('neighborhood', data.neighborhood)
				setValue('state', data.state)
			}).catch(() => {
				// setBairro('')
				// setCity('')
			})
		}
	}

	useEffect(() => {
		if (zipCode) { getCep(zipCode.replace(/[^\d]/g, '')) }
	}, [zipCode])

	return (
		<>
			{userExists && !isEditForm ? (
				<>
					<Input
						placeholder="E-mail"
						label="E-mail"
						type="email"
						error={errors.email}
						{...register("email")}
					/>
					<Text textStyle="textMD"><Text as="span" textStyle="titleMD">{name}</Text> {textUserExists}</Text>
				</>
			) : (
				<>
					<Grid
						templateColumns={{
							sm: 'repeat(4, 1fr)',
							md: 'repeat(8, 1fr)',
							lg: 'repeat(10, 1fr)',
							xl: 'repeat(12, 1fr)',
							'2xl': 'repeat(12, 1fr)',
						}}
						gap={6}
						w="100%"
					>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 4 }} rowSpan={{ sm: 8, lg: 2 }}>
							<InputImage
								name="avatarSecureId"
								label="Avatar"
								watch={watch}
								setValue={setValue}
								clearErrors={clearErrors}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, '2xl': 8 }}>
							<Input
								placeholder="Nome"
								label="Nome"
								error={errors.name}
								{...register("name")}
							/>
						</GridItem>

						<GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, '2xl': 8 }}>
							<Input
								placeholder="E-mail"
								label="E-mail"
								type="email"
								error={errors.email}
								{...register("email")}
							/>
						</GridItem>

						<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 4 }}>
							{isEditForm ? (
								<Input
								label="CPF"
								placeholder="CPF"
								error={errors.legal_document_number}
								{...register('legal_document_number', { onChange: () => {} })}
							/>
							) : (
							<InputMask
								label="CPF"
								placeholder="CPF"
								mask="999.999.999-99"
								error={errors.legal_document_number}
								{...register('legal_document_number', { onChange: () => {} })}
							/>
						)
						}
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 4 }}>
							<InputMask
								label="Celular"
								placeholder="Celular"
								mask="(99)99999-9999"
								error={errors.cell}
								{...register('cell', { onChange: () => {} })}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 4 }}>
							<InputDate
								label="Data de nascimento"
								placeholder="Data de nascimento"
								{...register('birth_date')}
								error={errors.birth_date}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
							<Divider />
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 3, '2xl': 2 }}>
							{isEditForm ? (
								<Input
								placeholder="Cep"
								label="Cep"
								error={errors.zip_code}
								{...register("zip_code")}
								/>
							) : (
								<InputMask
								placeholder="Cep"
								label="Cep"
								mask='99999-999'
								error={errors.zip_code}
								{...register("zip_code")}
								/>
							)}
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 7, xl: 9, '2xl': 8 }} >
							<Input
								placeholder="Endereço"
								label="Endereço"
								error={errors.street}
								{...register("street")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 2, xl: 3, '2xl': 2 }}>
							<Input
								placeholder="Número"
								label="Número"
								error={errors.number}
								{...register("number")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 8, xl: 9, '2xl': 4 }}>
							<Input
								placeholder="Complemento"
								label="Complemento"
								error={errors.complement}
								{...register("complement")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 3 }}>
							<Input
								placeholder="Bairro"
								label="Bairro"
								error={errors.neighborhood}
								{...register("neighborhood")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 3 }}>
							<Input
								placeholder="Cidade"
								label="Cidade"
								error={errors.city}
								{...register("city")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 2 }}>
							<Input
								placeholder="Estado"
								label="Estado"
								error={errors.state}
								{...register("state")}
							/>
						</GridItem>
            <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
							<InputPassword
								label="Senha"
								placeholder="Senha"
								error={errors.password}
								{...register("password")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
							<InputPassword
								label="Confirmação de Senha"
								placeholder="Repetir a Senha"
								error={errors.passwordConfirmation}
								{...register("passwordConfirmation")}
							/>
						</GridItem>
					</Grid>
				</>
			)}
		</>
	)
}
