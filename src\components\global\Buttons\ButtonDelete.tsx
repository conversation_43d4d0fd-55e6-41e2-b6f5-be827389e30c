import {
	Box,
	Button,
	ButtonProps,
	Flex,
	H<PERSON><PERSON>ck,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	Text,
} from "@chakra-ui/react"
import { UseMutationResult } from "@tanstack/react-query"
import { useRef, useState } from "react"

import { useForm } from "react-hook-form"
import { IoMdTrash } from "react-icons/io"

import { PopoverComponent } from "../Popover"
import { ButtonCancelSubmit } from "./ButtonCancelSubmit"
import { ButtonSubmit } from "./ButtonSubmit"

interface ButtonDeleteProps extends ButtonProps {
	deleteFunction: UseMutationResult<unknown, unknown, void, unknown>
	titlePopover: string
	tooltipLabel: string
	message: string
}

export function ButtonDelete({ deleteFunction, titlePopover, tooltipLabel, message }: ButtonDeleteProps) {
	const { formState, handleSubmit } = useForm()
	const [isOpenPopover, setIsOpenPopover] = useState(false)
	const firstFieldRef = useRef(null)

	function onClosePopover() {
		setIsOpenPopover(false)
	}

	function onOpenPopover() {
		setIsOpenPopover(true)
	}

	const handleChangeStatus = async () => {
		try {
			await deleteFunction.mutateAsync()
			onClosePopover()
		} catch { }
	}

	return (
		<Box>
			<PopoverComponent
				isOpen={isOpenPopover}
				initialFocusRef={firstFieldRef}
				onOpen={onOpenPopover}
				onClose={onClosePopover}
				title={titlePopover}
				tooltipLabel={tooltipLabel}
				body={
					<Flex
						as="form"
						flexDirection="column"
						onSubmit={handleSubmit(handleChangeStatus)}
					>
						<Stack spacing="6">
							<Text textStyle="textMD" whiteSpace="normal">
								{message}
							</Text>
						</Stack>
						<PopoverFooter display="flex" justifyContent="flex-end" pt="5" mt="2">
							<Flex justify="flex-end" w="100%">
								<HStack spacing="4" width="20em">
									<ButtonCancelSubmit onClick={onClosePopover}>
										Cancelar
									</ButtonCancelSubmit>
									<ButtonSubmit isLoading={formState.isSubmitting}>
										Remover
									</ButtonSubmit>
								</HStack>
							</Flex>
						</PopoverFooter>
					</Flex>
				}
			>
				<Button
					size="sm"
					fontSize="sm"
					colorScheme="red"
				>
					<Icon
						as={IoMdTrash}
						fontSize="20"
					/>
				</Button>
			</PopoverComponent>
		</Box>
	)
}
