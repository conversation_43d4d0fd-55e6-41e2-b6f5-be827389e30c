import {
	<PERSON>,
	<PERSON>lex,
	<PERSON><PERSON><PERSON><PERSON>,
	Spinner,
	<PERSON>,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack
} from "@chakra-ui/react"

import { useControlFilters } from "~/contexts/ControlFiltersContext"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"
import { CardDependentAdmin } from "~/components/Admin/Patient/TabDependents/CardDependentAdmin"
import { useDependentsPartner } from "~/hooks/Partner/Dependents/useDependentsPartner"
import { CardDependentPartner } from "./CardDependentPartner"


interface TabDependentsProps {
	parent: string
}

export function TabDependentsPartner({ parent }: TabDependentsProps) {
	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters()

	const { data, isLoading, error, isFetching } = useDependentsPartner({ page, search, limit, parent })

	return (
		<VStack spacing="4">
			<VStack width="100%" p="4" align="flex-end">
					<ButtonToCreate linkHref={`/partner/patients/${parent}/dependents/add`}>
						Novo
					</ButtonToCreate>
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
					<HStack spacing="4" align="center">
						{isFetching && !isLoading && (
							<Spinner />
						)}
						<Box w="72">
							<InputSearch
								name="search"
								placeholder="Nome"
								setPage={setPage}
								setSearch={setSearch}
							/>
						</Box>
					</HStack>
				</Flex>
				{data && (
					<>
						<TableContainer w="100%">
							<Table>
								<Thead>
									<Tr>
										<Th>Nome</Th>
										<Th>CPF</Th>
										<Th>Data de nascimento</Th>
											<Th>
												<Text
													align="center"
												>
													Ações
												</Text>
											</Th>
									</Tr>
								</Thead>
								<Tbody>
									{data.dependents.map(dependent => (
										<CardDependentPartner
											key={dependent.secure_id}
											parentSecureId={parent}
											secureId={dependent.secure_id}
											name={dependent.name}
											email={dependent.email}
											birth_date={dependent.birth_date}
											legal_document_number={dependent.legal_document_number}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.dependents.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}
