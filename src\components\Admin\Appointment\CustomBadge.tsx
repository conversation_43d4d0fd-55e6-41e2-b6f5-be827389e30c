import { Badge, Flex } from "@chakra-ui/react";
import { IoClose } from "react-icons/io5";

type Props = {
	title: string;
	handleClick: () => void;
}

export function CustomBadge({ title, handleClick }: Props) {
	return (
		<Badge 
			pl={4}
			py={1}
			borderRadius={6}
			display='flex'
			flexDir='row'
			justifyContent='space-between'
			alignItems='center'
			as="div"
			gap={3}
			cursor='pointer'
			onClick={handleClick}
			maxWidth="100%"
			whiteSpace="normal"
			overflowWrap="break-word"
			wordBreak="break-word"
		>
			{title}
			
			<IoClose size={16} style={{ marginRight: '8px'}} />
		</Badge>
	)
}
