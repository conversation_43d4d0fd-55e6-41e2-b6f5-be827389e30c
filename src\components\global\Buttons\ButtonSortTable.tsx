import { Button, ButtonGroup, ButtonGroupProps, Icon, IconButton } from "@chakra-ui/react"
import { <PERSON>spatch, FC, SetStateAction } from "react"
import { RiArrowDownSLine, RiArrowUpSLine } from "react-icons/ri"

interface ButtonSortTableProps extends ButtonGroupProps {
	label: string
	field: string
	currentField: string
	direction: 'asc' | 'desc'
	setField: Dispatch<SetStateAction<string>>
	setDirection: Dispatch<SetStateAction<"asc" | "desc">>
}

export const ButtonSortTable: FC<ButtonSortTableProps> = ({ label, direction, setDirection, setField, field, currentField, ...rest }) => {
	return (
		<ButtonGroup size='sm' isAttached variant='outline' {...rest}>
			<Button onClick={() => setField(field)}>{label}</Button>
			{currentField === field && (
				<IconButton
					aria-label="Direction sort"
					icon={<Icon as={direction === 'asc' ? RiArrowUpSLine : RiArrowDownSLine} fontSize="20" />}
					onClick={() => {
						if (direction) {
							setDirection(oldValue => oldValue === 'asc' ? 'desc' : 'asc')
						}
					}}
				/>
			)}
		</ButtonGroup>
	)
}
