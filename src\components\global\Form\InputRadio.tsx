import {
	Radio,
	RadioGroup,
	FormControl,
	FormErrorMessage,
	FormLabel,
	Stack,
	Flex,
} from '@chakra-ui/react'
import { FieldError, Controller, Control } from 'react-hook-form'

import { FC } from 'react'
import { Option } from '~/utils/Types/Global'

interface InputRadioProps {
	name: string
	label?: string
	placeholder?: string
	error?: FieldError
	control: Control<any, any>
	options: Option[]
	direction?: 'column' | 'row'
	isDisabled?: boolean
}

const InputRadioBase: FC<InputRadioProps> = ({ name, label, control, options, direction = 'column', error = null, isDisabled = false }) => {
	return (
		<FormControl isInvalid={!!error}>
			{!!label &&
				<FormLabel
					w="100%"
					htmlFor={name}
					display="flex"
					alignItems="center"
					fontWeight="bold"
				>
					{label}
				</FormLabel>
			}
			<Controller
				name={name}
				control={control}
				render={({ field: { ref, value, ...rest } }) => (
					<Flex h={!label ? '100%' : 'inherit'} align={!label ? 'center' : 'inherit'} justify={!label ? 'center' : 'inherit'}>
						<RadioGroup {...rest} value={value} isDisabled={isDisabled}>
							<Stack spacing={2} direction={direction}>
								{options.map(option => (
									<Radio key={option.value} value={String(option.value)}>{option.label}</Radio>
								))}
							</Stack>
						</RadioGroup>
					</Flex>
				)}
			/>
			{error &&
				<FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
			}
		</FormControl>
	)
}

export const InputRadio = InputRadioBase
