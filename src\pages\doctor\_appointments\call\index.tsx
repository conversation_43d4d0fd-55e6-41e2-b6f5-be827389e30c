import {
  Button,
  Flex,
  Text,
  useBreakpointValue,
  useToast,
} from "@chakra-ui/react";
import React, { useCallback, useEffect, useState } from "react";
import VideoCallDoctor from "~/components/Doctor/VideoCall/VideoCallDoctor";
import Video, { RemoteParticipant, Room } from "twilio-video";
import {
  cancelCall,
  useVideoCallAuthContext,
} from "~/contexts/VideoCallAuthContext";
import { useRouter } from "next/router";
import { useMutation } from "@tanstack/react-query";
import { api } from "~/services/apiClient";
import { AxiosError, AxiosResponse } from "axios";
import { queryClient } from "~/services/queryClient";

export default function VideoCall() {
  const isWideVersion = useBreakpointValue({
    base: false,
    md: true,
  });

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [room, setRoom] = useState<Room | null>(null);
  const [participants, setParticipants] = useState<RemoteParticipant[]>([]);
  const {
    videoToken,
    roomName,
    userName,
    setVideoCredentials,
    setRoomContext,
  } = useVideoCallAuthContext();

  const router = useRouter();
  const toast = useToast();

  const changeStatus = useMutation(
    async () => {
      return await api.put(`/v1/admin/appointments-doctors/${roomName}`);
    },
    {
      onSuccess: (response: AxiosResponse) => {
        queryClient.invalidateQueries(["AppointmentDoctor"]);
        toast({
          title: response.data?.message || "Consulta realizada com sucesso!",
          position: "top-right",
          status: response.data?.type || "success",
          isClosable: true,
        });
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title: error?.response?.data?.message || "Erro ao realizar consulta.",
          position: "top-right",
          status: error?.response?.data?.type || "error",
          isClosable: true,
        });
      },
    }
  );

  const handleLogout = useCallback(async () => {
    try {
      await changeStatus.mutateAsync();
      room?.localParticipant.videoTracks.forEach((track) => {
        track.track.stop();
        track.unpublish();
        track.track.disable();
      });
      room?.localParticipant.audioTracks.forEach((track) => {
        track.track.stop();
        track.unpublish();
        track.track.disable();
      });
      setVideoCredentials({
        roomName: "",
        token: "",
        userName: "",
      });
      room?.disconnect();
      cancelCall();
      router.push("/doctor/appointments");
    } catch (error) {}
  }, [router, room]);

  const disconnect = (currentRoom: Room | null) => {
    if (currentRoom && currentRoom.localParticipant.state === "connected") {
      currentRoom.localParticipant.tracks.forEach(function (
        trackPublication: any
      ) {
        trackPublication.track.stop();
      });
      currentRoom.disconnect();
      return null;
    } else {
      return currentRoom;
    }
  };

  useEffect(() => {
    const participantConnected = (participant: RemoteParticipant) => {
      setParticipants((prevParticipants) => [...prevParticipants, participant]);
    };
    setIsLoading(true);

    if (
      videoToken &&
      roomName &&
      !(room?.localParticipant.state === "connected")
    ) {
      Video.connect(videoToken, {
        name: roomName,
      }).then((room) => {
        setRoom(room);
        setRoomContext(room);
        room.on("participantConnected", participantConnected);
        room.participants.forEach(participantConnected);
        room.on("disconnected", () => {
          setRoom(null);
          setRoomContext(null);
        });
      });
    }

    setIsLoading(false);

    return () => {
      setRoom(disconnect);
      setRoomContext(disconnect);
    };
  }, [videoToken, roomName, userName]);

  return (
    <>
      <Flex
        w="100%"
        layerStyle="container"
        justify="space-between"
        py={4}
        direction={isWideVersion ? "row" : "column"}
        align="center"
        gap={4}
      >
        <Text textStyle="headerLG" as="header">
          {userName}
        </Text>
        <Button
          colorScheme="green"
          size="md"
          loadingText="Carregando"
          spinnerPlacement="end"
          onClick={handleLogout}
        >
          Finalizar consulta
        </Button>
      </Flex>

      <VideoCallDoctor
        handleLogout={handleLogout}
        participant={room?.localParticipant}
        participants={participants}
        isLoading={isLoading}
      />
    </>
  );
}
