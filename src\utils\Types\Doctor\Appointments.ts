export interface ListAppointmentDoctor {
  secure_id: string;
  partner_type: string;
  date: string;
	status: 'waiting' | 'realized' | 'canceled';
  date_canceled: string;
  type_canceled: any;
  motive_canceled: any;
  created_at: string;
  updated_at: string;
  specialty: Specialty;
  exam: any;
  partner: Partner;
  schedule: Schedule;
  patient: Patient;
}

export interface Specialty {
  name: string;
}

export interface Partner {
  userInfo: UserInfoPartner;
}

export interface UserInfoPartner {
  name: string;
}

export interface Schedule {
  secure_id: string;
  type_consult: "in_person" | "video_call" | "exam";
  status: "waiting_backoffice" | "waiting_patient" | "approved" | "canceled";
  neighborhood: string;
  city: string;
  state: string;
  date_canceled: string;
  type_canceled: any;
  motive_canceled: any;
  created_at: string;
  updated_at: string;
}

export interface Patient {
  userInfo: UserInfoPatient;
}

export interface UserInfoPatient {
  name: string;
}
