export const cnpjValidation = (cnpj: string): boolean => {
  if (!cnpj) {
    return false;
  }

  switch (cnpj) {
    case '00000000000000':
      return false;
    case '11111111111111':
      return false;
    case '22222222222222':
      return false;
    case '33333333333333':
      return false;
    case '44444444444444':
      return false;
    case '55555555555555':
      return false;
    case '66666666666666':
      return false;
    case '77777777777777':
      return false;
    case '88888888888888':
      return false;
    case '99999999999999':
      return false;
  }

  // Remover caracteres não numéricos
  cnpj = cnpj.replace(/[^\d]/g, '');

  // Verificar se o CNPJ possui 14 dígitos
  if (cnpj.length !== 14) {
    return false;
  }

  // Verificar se todos os dígitos são iguais (caso contrário, é um CNPJ inválido)
  if (/^(\d)\1+$/.test(cnpj)) {
    return false;
  }

  // Calcular o primeiro dígito verificador
  let soma = 0;
  let peso = 5;
  for (let i = 0; i < 12; i++) {
    soma += parseInt(cnpj.charAt(i)) * peso;
    peso = peso === 2 ? 9 : peso - 1;
  }
  let digitoVerificador1 = 11 - (soma % 11);
  digitoVerificador1 = digitoVerificador1 >= 10 ? 0 : digitoVerificador1;

  // Verificar se o primeiro dígito verificador está correto
  if (digitoVerificador1 !== parseInt(cnpj.charAt(12))) {
    return false;
  }

  // Calcular o segundo dígito verificador
  soma = 0;
  peso = 6;
  for (let i = 0; i < 13; i++) {
    soma += parseInt(cnpj.charAt(i)) * peso;
    peso = peso === 2 ? 9 : peso - 1;
  }
  let digitoVerificador2 = 11 - (soma % 11);
  digitoVerificador2 = digitoVerificador2 >= 10 ? 0 : digitoVerificador2;

  // Verificar se o segundo dígito verificador está correto
  if (digitoVerificador2 !== parseInt(cnpj.charAt(13))) {
    return false;
  }

  // Se todas as verificações passaram, o CNPJ é válido
  return true;
};