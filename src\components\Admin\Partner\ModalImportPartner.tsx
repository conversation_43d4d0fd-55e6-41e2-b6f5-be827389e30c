import {
	Flex,
	HStack,
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
	useToast
} from "@chakra-ui/react"
import { FC, useEffect, useMemo } from "react"

import { parse } from "date-fns"

import * as yup from 'yup'
import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { read, utils } from 'xlsx'

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { InputDocFile } from "~/components/global/Form/InputDocFile"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"

const FormSchema = yup.object().shape({
	file: yup.mixed().required('Você deve importar o arquivo Excel'),
	// users: yup.array().of(yup.object({
	// 	name: yup.string().required(),
	// 	email: yup.string().required(),
	// 	legalDocumentNumber: yup.string().required(),
	// 	dddCell: yup.number().required(),
	// 	cell: yup.number().required(),
	// 	birthDate: yup.string().required(),
	// 	adviceRegister: yup.string().required(),
	// 	paymentMethods: yup.string().required(),
	// 	typeOfCare: yup.mixed<'in_person' | 'video_call' | 'both'>().oneOf(['in_person', 'video_call', 'both']).required(),
	// 	type: yup.mixed<'doctor' | 'clinic' | 'hospital' | 'lab'>().oneOf(['doctor', 'clinic', 'hospital', 'lab']).required()
	// }).required()).optional()
})

type FormData = {
	file: File
	users?: {
		name: string
		email: string
		legalDocumentNumber: string
		password?: string
	}[]
}

interface ModalImportPartnerProps {
	isOpen: boolean
	closeModal: () => void
}

export const ModalImportPartner: FC<ModalImportPartnerProps> = ({ closeModal, isOpen }) => {
	const toast = useToast()

	const { handleSubmit, register, formState, watch, control, reset, setValue } = useForm<FormData>({
		resolver: yupResolver<any>(FormSchema),
		defaultValues: {
		}
	})
	const { errors } = formState

	const file = watch("file")
	const users = watch("users")

	const readExcel = (fileRead: File) => {
		const promise = new Promise((resolve, reject) => {
			const fileReader = new FileReader()
			fileReader.readAsArrayBuffer(fileRead)

			fileReader.onload = (e) => {
				const bufferArray = e.target?.result

				const wb = read(bufferArray, {
					type: 'file',
					cellDates: true,
					cellText: false,
				})

				const wsname = wb.SheetNames[0]

				const ws = wb.Sheets[wsname]

				const data = utils.sheet_to_json(ws, { raw: false, dateNF: "DD/MM/YYYY" })

				resolve(data)
			}
		})

		promise.then((d: any) => {
			setValue("users", d.map((user: any) => {
				return {
					name: user['Nome'],
					email: user['E-mail'],
					legalDocumentNumber: user['CPF'],
					password: user['Senha (Não obrigatório)'],
				}
			}))
		}).catch(() => {
			setValue("users", [])
		})
	}

	const dataExportExcel = useMemo(() => {
		return new Array(1).fill(null).map((_, index) => ({
			'Nome': `Usuário`,
			'E-mail': `usuario${index + 1}@exemplo.com.br`,
			'CPF': `000.000.000-00`,
			'Senha (Não obrigatório)': 'Não obrigatório'
		}))
	}, [])

	const add = useMutation(async (values: FormData) => {
		return await api.post(`/v1/admin/import-partners`, {
			users: values.users,
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['PartnersAdmin'])
			handleCloseModal()
			toast({
				title: response.data?.message || `Parceiros importados com sucesso!`,
				position: "top-right",
				status: "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || error?.response?.data?.errors[0].message || 'Ocorreu um problema ao importar parceiros.',
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	})

	const handleAdd: SubmitHandler<FormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch {}
	}

	const handleCloseModal = () => {
		closeModal()
		reset()
	}

	useEffect(() => {
		if (file) {
			readExcel(file)
		}
	}, [file])

	return (
		<Modal size="md" isOpen={isOpen} onClose={handleCloseModal} closeOnOverlayClick={false}>
			<ModalOverlay />
			<ModalContent
			>
				<ModalHeader>Importar</ModalHeader>
				<ModalCloseButton />
				<ModalBody>
					<VStack
						as="form"
						width="100%"
						p="4"
						spacing={["6", "8"]}
						flexDirection="column"
						align="flex-start"
						justify="center"
						onSubmit={handleSubmit(handleAdd)}
					>
						<ButtonExportExcel
							variant="link"
							isDisabled={dataExportExcel.length === 0}
							data={dataExportExcel}
							fileName={`modelo_importar_parceiro`}
						>
							Exportar modelo
						</ButtonExportExcel>
						<InputDocFile
							watch={watch}
							control={control}
							error={errors.file}
							{...register('file')}
						/>
						<Flex justify="flex-end" w="100%">
							<HStack spacing="4" width="20em">
								<ButtonCancelSubmit onClick={handleCloseModal}>Cancelar</ButtonCancelSubmit>
								<ButtonSubmit isLoading={formState.isSubmitting} isDisabled={!users}>Salvar</ButtonSubmit>
							</HStack>
						</Flex>
					</VStack>
				</ModalBody>
			</ModalContent>
		</Modal>
	)
}
