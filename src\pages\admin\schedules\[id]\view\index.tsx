import {
	Divider,
	Flex,
	Grid,
	Grid<PERSON>tem,
	HStack,
	Image,
	Link,
	Stack,
	Table,
	TableContainer,
	Tbody,
	Td,
	Text,
	Th,
	Thead,
	Tr,
	VStack
} from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { GetServerSideProps, NextPage } from "next";
import { useCallback } from "react";
import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog";

import { api } from "~/services/apiClient";
import { FormatDateForDayMonthYearUsingBars, FormatDateForHourMinutes } from "~/utils/Functions/FormatDates";
import { ScheduleDatesRequestsProps, ScheduleShowProps } from "~/utils/Types/Admin/Schedule";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";

interface ScheduleEditProps {
	scheduleSecureId: string;
}

const ScheduleEdit: NextPage<ScheduleEditProps> = ({ scheduleSecureId }) => {
	const { data: scheduleData, isLoading } = useQuery(["schedules", scheduleSecureId], async () => {
		const response = await api.get(`/v1/admin/schedules/${scheduleSecureId}`)
		return response.data as ScheduleShowProps
	})

	const checkDateType = useCallback((data: ScheduleDatesRequestsProps) => {
		if (data.date_type === 'period') {
			switch (data.value) {
				case 'morning':
					return 'Manhã'

				case 'afternoon':
					return 'Tarde'

				case 'night':
					return 'Noite'
				default:
					return ''
			}
		}
		return FormatDateForHourMinutes(data.date)
	}, [])

	const checkStatus = useCallback((data: ScheduleDatesRequestsProps) => {
		switch (data.status) {
			case 'to_check':
				return { text: 'Checar', color: 'orange.400' }

			case 'unavailable':
				return { text: 'Indisponível', color: 'red' }

			case 'available':
				return { text: 'Disponível', color: 'green' }
			default:
				return { text: '', color: '' }
		}
	}, [])

	return (
		<VStack spacing="4" layerStyle="container">
			<Stack
				w="100%" background="white" rounded="md" shadow="md" padding={4}
			>
				<Grid templateColumns={{
					sm: 'repeat(4, 1fr)',
					md: 'repeat(8, 1fr)',
					lg: 'repeat(10, 1fr)',
					xl: 'repeat(12, 1fr)',
					'2xl': 'repeat(12, 1fr)',
				}} gap={6}>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }} >
						<Text
							fontSize="xs"
						>
							Solicitação
						</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">{scheduleData?.type_consult !== 'exam' ? 'Consulta com:' : 'Exame:'}</Text>
							<Text>{scheduleData?.specialty?.name}{scheduleData?.exam?.name}</Text>
						</HStack>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }} >
						<Text
							fontSize="xs"
						>
							Paciente
						</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">Nome: </Text>
							<Text>{scheduleData?.patient.userInfo.name}</Text>
						</HStack>
						{scheduleData?.patient.userInfo.phone && <HStack>
							<Text fontSize="sm" fontWeight="medium">Telefone: </Text>
							<Text>({scheduleData?.patient.userInfo.ddd_phone}) - {scheduleData?.patient.userInfo.phone}</Text>
						</HStack>}
						{scheduleData?.patient.userInfo.cell && <HStack>
							<Text fontSize="sm" fontWeight="medium">Celular: </Text>
							<Text>({scheduleData?.patient.userInfo.ddd_cell}) - {scheduleData?.patient.userInfo.cell}</Text>
						</HStack>}
					</GridItem>
					{scheduleData?.patient?.parent && <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }} >
						<Stack>
							<Text
								fontSize="xs"
							>
								Responsável
							</Text>
							<Divider />
							<HStack>
								<Text fontSize="sm" fontWeight="medium">Nome: </Text>
								<Text>{scheduleData?.patient?.parent.userInfo.name}</Text>
							</HStack>
							{scheduleData?.patient?.parent.userInfo.phone && <HStack>
								<Text fontSize="sm" fontWeight="medium">Telefone: </Text>
								<Text>({scheduleData?.patient?.parent.userInfo.ddd_phone}) - {scheduleData?.patient?.parent.userInfo.phone}</Text>
							</HStack>}
							{scheduleData?.patient?.parent.userInfo.cell && <HStack>
								<Text fontSize="sm" fontWeight="medium">Celular: </Text>
								<Text>({scheduleData?.patient?.parent.userInfo.ddd_cell}) - {scheduleData?.patient?.parent.userInfo.cell}</Text>
							</HStack>}
						</Stack>
					</GridItem>}
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }} >
						<Text fontSize="xs">Local</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">Bairro: </Text>
							<Text>{scheduleData?.neighborhood}</Text>
						</HStack>
						<HStack>
							<Text fontSize="sm" fontWeight="medium">Cidade: </Text>
							<Text>{scheduleData?.city}</Text>
						</HStack>
						<HStack>
							<Text fontSize="sm" fontWeight="medium">Estado: </Text>
							<Text>{scheduleData?.state}</Text>
						</HStack>
					</GridItem>
					{scheduleData?.uploads && scheduleData?.uploads?.length > 0 && (
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
							<Text fontSize="xs">Solicitação de Exame</Text>
							<Divider />
							{scheduleData?.uploads.map(item => (
								<Link href={item.url} target="_blank" key={item.url}>
									<Image src={item.url} w={14} h={16} />
								</Link>
							))}

						</GridItem>
					)}
				</Grid>

			</Stack>

			<Stack w="100%" background="white" rounded="md" shadow="md" padding={4}>
				<Flex>
				</Flex>
				<TableContainer>
					<Table variant='simple'>
						<Thead>
							<Tr>
								<Th>Médico / Clinica / Laboratório</Th>
								<Th>Telefone</Th>
								<Th>Data</Th>
								<Th>Horário</Th>
								<Th>Disponibilidade</Th>
							</Tr>
						</Thead>
						<Tbody>
							{scheduleData?.scheduleDatesRequests.map((item, index) => (
								<Tr key={item.secure_id}>
									<Td>{item.partner && item.partner.userInfo ? item.partner.userInfo.name : 'helloMed'}</Td>
									<Td>{item.partner && item.partner.userInfo ? ` (${item.partner.userInfo.ddd_cell})${item.partner.userInfo.cell}` : ''}</Td>
									<Td >{FormatDateForDayMonthYearUsingBars(item.date)}</Td>
									<Td>{checkDateType(item)}</Td>
									<Td color={checkStatus(item).color}>{checkStatus(item).text}</Td>
								</Tr>
							))}
						</Tbody>
					</Table>
				</TableContainer>
			</Stack>

			<ListActionLog
				layerStyle="card"
				changedSecureId={scheduleSecureId}
				type="schedule"
			/>
		</VStack >
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		return {
			props: {
				scheduleSecureId: id
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["schedule_edit"],
	}
)

export default ScheduleEdit
