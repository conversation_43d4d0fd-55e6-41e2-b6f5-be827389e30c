import {
  FormControl,
  FormErrorMessage,
  FormLabel,
  Input as ChakraInput,
  InputProps as ChakraInputProps,
} from "@chakra-ui/react";
import {
  Control,
  FieldError,
  UseFormSetValue,
} from "react-hook-form";
import { NumberFormatValues, NumericFormat } from "react-number-format";
import { forwardRef, ForwardRefRenderFunction } from "react";

interface InputProps extends ChakraInputProps {
  name: string;
  setValue: UseFormSetValue<any>;
  label?: string;
  placeholder?: string;
  prefix?: string;
  error?: FieldError;
  inputInCard?: boolean;
  fixedDecimalScale?: boolean;
  decimalScale?: number;
  suffix?: string;
}

const InputNumberBase: ForwardRefRenderFunction<
  HTMLInputElement,
  InputProps
> = (
  {
    name,
    label,
    error = null,
    setValue,
    inputInCard = true,
    fixedDecimalScale = true,
    decimalScale = 2,
    placeholder,
    prefix,
    suffix,
    ...rest
  },
  ref
) => {
  return (
    <FormControl isInvalid={!!error}>
      {!!label && (
        <FormLabel
          w="100%"
          htmlFor={name}
          display="flex"
          alignItems="center"
          fontWeight="bold"
          color="textMD"
        >
          {label}
        </FormLabel>
      )}
      <ChakraInput
        as={NumericFormat}
        id={name}
        name={name}
        size="lg"
        variant="outline"
        fontSize={{ sm: "md", md: "lg" }}
        // campos para número
        onWheel={(event) => event.target.removeEventListener}
        thousandSeparator="."
        decimalSeparator=","
        autoComplete="off"
        decimalScale={decimalScale}
        fixedDecimalScale={fixedDecimalScale}
        isNumericString
        onValueChange={(value: NumberFormatValues) => {
          setValue(name, value.floatValue);
        }}
        placeholder={placeholder}
        prefix={prefix}
        suffix={suffix}
        allowNegative={false}
        ref={ref}
        {...rest}
      />
      {error && (
        <FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
      )}
    </FormControl>
  );
};

export const InputNumberMask = forwardRef(InputNumberBase);
