import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
} from "@chakra-ui/react"
import { FC } from "react"

import * as yup from "yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { ShowUser, UserFormData } from "~/utils/Types/Admin/User"
import { FormatDateForYearMonthDay } from "~/utils/Functions/FormatDates"

import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { GetServerSideProps } from "next"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { setupApiClient } from "~/services/api"
import { FormUserProfile } from "~/components/Admin/Profile/FormUserProfile"

const FormSchema = yup.object().shape({
	name: yup.string().required("Nome obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	password: yup.string().when("password", {
    is: (value: string) => !!value,
    then: (schema) => schema
      .required("Senha obrigatória")
      .min(8, "No mínimo 8 caracteres")
      .matches(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/])[A-Za-z\d!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/]{6,}$/,
        'A senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.'
      )
  }),
  passwordConfirmation: yup
    .string()
    .oneOf([yup.ref("password")], "As senha precisam ser iguais"),
	legal_document_number: yup.string().required("CPF obrigatório"),
	cell: yup.string().required("Celular obrigatório"),
	birth_date: yup.string().required("Data de nascimento obrigatória"),
}, [
	["password", "password"]
])

interface ProfileAdminProps {
	user: ShowUser
}

const ProfileAdmin: FC<ProfileAdminProps> = ({ user }) => {
	const toast = useToast()

	const {
		register,
		formState,
		handleSubmit,
		setValue,
		watch,
		clearErrors,
	} = useForm<UserFormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			name: user.userInfo.name,
			email: user.email,
			avatarSecureId: user.avatar ? user.avatar.secure_id : undefined,
			birth_date: user.userInfo.birth_date ? FormatDateForYearMonthDay(user.userInfo.birth_date) : "",
			legal_document_number: user.userInfo.legal_document_number,
			cell: `${user.userInfo.ddd_cell}${user.userInfo.cell}`,
			zip_code: user.userInfo.zip_code,
			number: user.userInfo.number,
			complement: user.userInfo.complement
		},
	})

	const edit = useMutation(
		async (values: UserFormData) => {
			const newCell = values.cell.replace(/\D/g, "");
      const ddd_cell = newCell.slice(0, 2);
      const cell = newCell.slice(2);

			return await api.put(`/v1/profile`, {
				...values,
				ddd_cell,
				cell
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				toast({
					title: response.data?.message || "Perfil alterado com sucesso!",
					position: "top-right",
					status: "success",
					isClosable: true,
				})
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao alterar perfil.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleEdit: SubmitHandler<UserFormData> = async (values) => {
		try {
			await edit.mutateAsync(values)
		} catch { }
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleEdit)}
			>
				<VStack spacing="4" align="flex-start">
					<FormUserProfile
						clearErrors={clearErrors}
						formState={formState}
						register={register}
						setValue={setValue}
						watch={watch}
						isEditForm
						textUserExists="isEditForm"
					/>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Salvar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const api = setupApiClient(ctx)
		const { data } = await api.get(`/v1/profile`)

		return {
			props: {
				user: data,
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
	}
)

export default ProfileAdmin
