import { api } from "../apiClient"
import { queryClient } from "../queryClient"

export async function updateScheduleStatusFromHistory(secure_id: string, status: string) {

  const response = await api.put(`/v1/admin/schedules/${secure_id}`, {
    status: status
  });

  await queryClient.invalidateQueries({queryKey: ['PatientHistory']})
  await queryClient.invalidateQueries({queryKey: ['ScheduleAdmin']})

  return response
}
