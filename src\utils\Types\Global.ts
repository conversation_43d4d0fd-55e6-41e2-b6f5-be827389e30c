export type Option = {
	label: string
	value: string | number
}

export const typesDoctors = [
	{ value: 'doctor', label: 'Médico' },
	{ value: 'doctor', label: 'MEDICO' },
	{ value: 'clinic', label: 'Clínica' },
	{ value: 'clinic', label: 'CLINICA' },
	{ value: 'hospital', label: 'Hospital' },
	{ value: 'hospital', label: 'HOSPITAL' },
	{ value: 'lab', label: 'Laboratório' },
	{ value: 'lab', label: 'LABORATORIO' },
]

export const typesStatus = [
	{ value: 'active', label: 'Ativo' },
	{ value: 'inactive', label: 'Inativo' },
	{ value: 'punctual', label: 'Pontual' },
]

export const typesOfCares = [
	{ value: 'in_person', label: 'Presencial' },
	{ value: 'video_call', label: 'Vídeo' },
	{ value: 'both', label: 'Ambos' },
]

export type TableExportExcel = {
	[label: string]: any
}

export type ActionLog = {
	type: 'user' | 'specialty' | 'exam'
	date: string
	charged_data: string
	user: {
		secure_id: string
		userInfo: {
			name: string
		}
	}
}

export const getAppointmentStatus = (status: string | undefined) => {
	switch (status) {
		case 'waiting_backoffice':
			return 'Aguardando Backoffice'

		case 'waiting_patient':
			return 'Aguardando Paciente'

		case 'approved':
			return 'Aprovada'

		case 'canceled':
			return 'Cancelada'

		default:
			break
	}
}
