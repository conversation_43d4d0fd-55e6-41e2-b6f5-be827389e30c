import {
	Flex,
	HStack,
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
	useToast
} from "@chakra-ui/react"
import { FC, useEffect, useMemo, useState } from "react"

import { parse } from "date-fns"

import * as yup from 'yup'
import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { read, utils } from 'xlsx'

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { InputDocFile } from "~/components/global/Form/InputDocFile"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"

const FormSchema = yup.object().shape({
	file: yup.mixed().required('Você deve importar o arquivo Excel'),
	// users: yup.array().of(yup.object({
	// 	name: yup.string().required(),
	// 	email: yup.string().required(),
	// 	legalDocumentNumber: yup.string().required(),
	// 	dddCell: yup.number().required(),
	// 	cell: yup.number().required(),
	// 	birthDate: yup.string().required(),
	// 	adviceRegister: yup.string().required(),
	// 	paymentMethods: yup.string().required(),
	// 	typeOfCare: yup.mixed<'in_person' | 'video_call' | 'both'>().oneOf(['in_person', 'video_call', 'both']).required(),
	// 	type: yup.mixed<'doctor' | 'clinic' | 'hospital' | 'lab'>().oneOf(['doctor', 'clinic', 'hospital', 'lab']).required()
	// }).required()).optional()
})

type FormData = {
	file: File | null
	users?: {
		name: string
		email: string
		legalDocumentNumber: string
		dddCell: number
		cell: number
		birthDate: string
		password?: string
		holderLegalDocumentNumber?: string
	}[]
}

interface ModalImportPartnerPatientProps {
	isOpen: boolean
	closeModal: () => void
	partnerId: string
}

export const ModalImportPartnerPatient: FC<ModalImportPartnerPatientProps> = ({ closeModal, isOpen, partnerId }) => {
	const toast = useToast()

	const [hasHolderError, setHasHolderError] = useState(false)

	const { handleSubmit, register, formState, watch, control, reset, setValue } = useForm<FormData>({
		resolver: yupResolver<any>(FormSchema),
		defaultValues: {
		}
	})
	const { errors } = formState

	const file = watch("file")
	const users = watch("users")

	const readExcel = (fileRead: File) => {
		const promise = new Promise((resolve, reject) => {
			const fileReader = new FileReader()
			fileReader.readAsArrayBuffer(fileRead)

			fileReader.onload = (e) => {
				const bufferArray = e.target?.result

				const wb = read(bufferArray, {
					type: 'file',
					cellDates: true,
					cellText: false,
				})

				const wsname = wb.SheetNames[0]

				const ws = wb.Sheets[wsname]

				const data = utils.sheet_to_json(ws, { raw: false, dateNF: "DD/MM/YYYY" })

				resolve(data)
			}
		})

		promise.then((d: any) => {
			// console.log('Inside Promise: ', d)
			setValue("users", d.map((user: any) => {
				const newCell = user?.['Celular']?.replace(/\D/g, "")
				const dddCell = newCell?.slice(0, 2)
				const cell = newCell?.slice(2)

				return {
					name: user['Nome'],
					email: user['E-mail'],
					legalDocumentNumber: user['CPF'],
					dddCell,
					cell,
					birthDate: parse(user['Data de Nascimento/Fundação (##/##/####)'], 'dd/MM/yyyy', new Date()),
					password: user['Senha (Não obrigatório)'],
					holderLegalDocumentNumber: user?.['CPF titular (Não obrigatório)'] ? user?.['CPF titular (Não obrigatório)'] : undefined,
				}
			}))
		}).catch((error) => {
			console.log('Error: ', error)
			setValue("users", [])
		})
	}

	const excelModel = useMemo(() => {
		return new Array(1).fill(null).map((_, index) => ({
			'Nome': `Usuário`,
			'E-mail': `usuario${index + 1}@exemplo.com.br`,
			'CPF': `000.000.000-00`,
			'Celular': '(00)00000-0000',
			'Data de Nascimento/Fundação (##/##/####)': '##/##/####',
			'Senha (Não obrigatório)': 'Não obrigatório',
			'CPF titular (Não obrigatório)': 'Não obrigatório',
		}))
	}, [])

	const excelErrors = useMemo(() => {
		if (users) {
			setHasHolderError(false)
			const legalDocumentNumbers = new Set();
			return users.map(user => {
				const hasHolderError = user.holderLegalDocumentNumber
					&& !legalDocumentNumbers.has(user.holderLegalDocumentNumber)
				setHasHolderError(!!hasHolderError)
				legalDocumentNumbers.add(user.legalDocumentNumber)

				return {
					'Nome': user.name,
					'E-mail': user.email,
					'CPF': user.legalDocumentNumber,
					'Celular': user.dddCell + '-' + user.cell, 
					'Data de Nascimento/Fundação (##/##/####)': user.birthDate,
					'Senha (Não obrigatório)': user.password,
					'CPF titular (Não obrigatório)': user.holderLegalDocumentNumber,
					'Erros': hasHolderError ? 'CPF titular não existe na base' : ''
				}
			})
		}

		return []
	}, [users])

	useEffect(() => {
		if (hasHolderError) {
			toast({
				title: `Erro na importação!`,
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	}, [hasHolderError])

	const add = useMutation(async (values: FormData) => {
		return await api.post(`/v1/admin/partners/${partnerId}/patients`, {
			users: values.users,
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['PartnerPatientsAdmin'])
			queryClient.invalidateQueries(["ActionLogsAdmin"])
			handleCloseModal()
			toast({
				title: `Pacientes importados com sucesso!`,
				position: "top-right",
				status: "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || error?.response?.data?.errors[0].message || 'Ocorreu um problema ao importar pacientes.',
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	})

	const handleAdd: SubmitHandler<FormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch {}
	}

	const handleCloseModal = () => {
		closeModal()
		reset()
	}

	useEffect(() => {
    if (file) {
      const LIMITE_EM_BYTES = 10 * 1024 * 1024; // 10MB (o mesmo do back-end)

      if (file.size > LIMITE_EM_BYTES) {
        toast({
          title: "Arquivo muito grande!",
          description: `O tamanho máximo permitido é de 10MB.`,
          status: "error",
          position: "top-right",
          isClosable: true,
        });
        setValue("file", null); 
        setValue("users", undefined);
        return;
      }
      readExcel(file)
    }
  }, [file, setValue, toast])

	return (
		<Modal size="md" isOpen={isOpen} onClose={handleCloseModal} closeOnOverlayClick={false}>
			<ModalOverlay />
			<ModalContent
			>
				<ModalHeader>Importar</ModalHeader>
				<ModalCloseButton />
				<ModalBody>
					<VStack
						as="form"
						width="100%"
						p="4"
						spacing={["6", "8"]}
						flexDirection="column"
						align="flex-start"
						justify="center"
						onSubmit={handleSubmit(handleAdd)}
					>
						<ButtonExportExcel
							variant="link"
							isDisabled={excelModel.length === 0}
							data={excelModel}
							fileName={`modelo_importar_pacientes`}
						>
							Baixar modelo
						</ButtonExportExcel>
						<InputDocFile
							watch={watch}
							control={control}
							error={errors.file}
							{...register('file')}
						/>
						{users && hasHolderError ? 
							<ButtonExportExcel
								variant="link"
								isDisabled={excelErrors.length === 0}
								data={excelErrors}
								fileName={`${file?.name.replace('.xlsx', '')}_erros`}
							>
								Tabela com erros
							</ButtonExportExcel>
						: null}
						<Flex justify="flex-end" w="100%">
							<HStack spacing="4" width="20em">
								<ButtonCancelSubmit onClick={handleCloseModal}>Cancelar</ButtonCancelSubmit>
								<ButtonSubmit isLoading={formState.isSubmitting} isDisabled={!users || hasHolderError}>Salvar</ButtonSubmit>
							</HStack>
						</Flex>
					</VStack>
				</ModalBody>
			</ModalContent>
		</Modal>
	)
}
