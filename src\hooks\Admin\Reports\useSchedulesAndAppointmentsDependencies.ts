import { useQuery } from "@tanstack/react-query"
import { useMemo } from "react"
import { api } from "~/services/apiClient"

type GetAppointmentResponse = {
  specialtiesAndExamsOptions: { label: string; value: string }[];
  statesOptions: { label: string; value: string }[];
}

export type AppointmentsAdminResponse = {
  specialties: { secure_id: string, name: string }[];
  exams: { secure_id: string, name: string }[];
  states: { name: string, uf: string }[];
}

export type Dependencies = {

}

export async function getSchedulesAndAppointmentsDependencies(): Promise<GetAppointmentResponse> {
  const dependenciesResponse = await api.get<AppointmentsAdminResponse>('/v1/admin/appointments-dependencies')


  const formattedSpecialtiesAsSelect = dependenciesResponse?.data.specialties.map((specialty) => {
    return {
      label: specialty.name,
      value: specialty.secure_id
    }
  });

  const formattedExamsAsSelect = dependenciesResponse?.data.exams.map((exam) => {
    return {
      label: exam.name,
      value: exam.secure_id
    }
  });

  const allStates: any = [];

  const formattedStatesAsSelect = dependenciesResponse.data.states.map((state) => {
    return {
      label: state.name,
      value: state.uf
    }
  });


  const allSpecialtiesAndExams: any = [];

  const formattedStates = [
    ...allStates,
    ...formattedStatesAsSelect
  ];

  const formattedSpecialtiesAndExams = [
    ...allSpecialtiesAndExams,
    ...formattedSpecialtiesAsSelect,
    ...formattedExamsAsSelect
  ];

  const dependencies = {
    specialtiesAndExamsOptions: formattedSpecialtiesAndExams,
    statesOptions: formattedStates
  }

  return {
    ...dependencies
  }
}

export function useSchedulesAndAppointmentsDependencies() {
  return useQuery(['AppointmentDependencies'], () => getSchedulesAndAppointmentsDependencies())
}
