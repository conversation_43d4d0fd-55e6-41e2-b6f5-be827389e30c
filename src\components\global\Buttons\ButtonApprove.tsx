import {
  Box,
  Button,
  ButtonProps,
  Flex,
  H<PERSON><PERSON>ck,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Text,
} from "@chakra-ui/react";
import { useRef, useState } from "react";

import { useForm } from "react-hook-form";
import { UseMutationResult } from "@tanstack/react-query";

import { BsToggle2On, BsToggle2Off, BsCheck2 } from "react-icons/bs";
import { RiUserFollowLine, RiUserUnfollowLine } from "react-icons/ri";

import { PopoverComponent } from "../Popover";
import { ButtonCancelSubmit } from "./ButtonCancelSubmit";
import { ButtonSubmit } from "./ButtonSubmit";

interface ButtonApproveProps extends ButtonProps {
  changeStatus?: UseMutationResult<unknown, unknown, void, unknown>;
  handleConfirm?: () => Promise<void>;
  titlePopover: string;
  tooltipLabel: string;
  message: string;
  confirmTitle?: string;
}

export function ButtonApprove({
  changeStatus,
  titlePopover,
  tooltipLabel,
  message,
  confirmTitle,
  handleConfirm,
  ...rest
}: ButtonApproveProps) {
  const { formState, handleSubmit } = useForm();
  const [isOpenPopover, setIsOpenPopover] = useState(false);
  const firstFieldRef = useRef(null);

  function onClosePopover() {
    setIsOpenPopover(false);
  }

  function onOpenPopover() {
    setIsOpenPopover(true);
  }

  const handleChangeStatus = async () => {
    try {
      if (changeStatus) {
        await changeStatus.mutateAsync();
      }
      if (handleConfirm) {
        handleConfirm();
      }
      onClosePopover();
    } catch {}
  };

  return (
    <Box>
      <PopoverComponent
        isOpen={isOpenPopover}
        initialFocusRef={firstFieldRef}
        onOpen={onOpenPopover}
        onClose={onClosePopover}
        title={titlePopover}
        tooltipLabel={tooltipLabel}
        body={
          <Flex
            as="form"
            flexDirection="column"
            onSubmit={handleSubmit(handleChangeStatus)}
          >
            <Stack spacing="6">
              <Text textStyle="textMD" whiteSpace="normal">
                {message}
              </Text>
            </Stack>
            <PopoverFooter
              display="flex"
              justifyContent="flex-end"
              pt="5"
              mt="2"
            >
              <Flex justify="flex-end" w="100%">
                <HStack spacing="4" width="20em">
                  <ButtonCancelSubmit onClick={onClosePopover}>
                    Cancelar
                  </ButtonCancelSubmit>
                  <ButtonSubmit isLoading={formState.isSubmitting}>
                    {confirmTitle ? confirmTitle : "Alterar"}
                  </ButtonSubmit>
                </HStack>
              </Flex>
            </PopoverFooter>
          </Flex>
        }
      >
        <PopoverTrigger>
          <Button size="sm" fontSize="sm" colorScheme="green" {...rest}>
            <Icon as={BsCheck2} fontSize="20" />
          </Button>
        </PopoverTrigger>
      </PopoverComponent>
    </Box>
  );
}
