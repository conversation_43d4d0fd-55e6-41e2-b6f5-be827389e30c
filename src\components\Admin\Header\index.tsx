import {
	Box,
	Flex,
	Grid,
	HStack,
	Icon,
	IconButton,
	Image,
	Menu,
	MenuButton,
	MenuList,
	Text,
	useBreakpointValue
} from '@chakra-ui/react'
import Router, { useRouter } from 'next/router'
import { FC } from 'react'

import { RiMenuLine } from 'react-icons/ri'
import { IoIosArrowDown } from 'react-icons/io'

import { queryClient } from '~/services/queryClient'
import { useAuthContext } from '~/contexts/AuthContext'
import { useSidebarDrawer } from '~/contexts/SideBarDrawerContext'

import { MenuItem } from './MenuItem'

export const HeaderAdmin: FC = () => {
	const router = useRouter();
  const currentRoute = router.asPath.split("/")[1];
	const { signOut, user } = useAuthContext()
	const { onOpen, isOpen } = useSidebarDrawer()

	const isWideVersion = useBreakpointValue({
		base: false,
		xl: true,
	})

	return (
		<Flex
			as="header"
			w="100%"
			h="20"
			px="6"
			ml="auto"
			shadow="md"
			bg="primary"
			justifyContent="space-between"
			align="center"
			top="0"
			position="sticky"
			zIndex={2}
			opacity="1"
			visibility="visible"
		>
			{!isWideVersion && (
				<IconButton
					aria-label="Open navigation"
					icon={<Icon as={RiMenuLine} />}
					fontSize="24"
					variant="unstyled"
					onClick={onOpen}
					mr="2"
				/>
			)}

			<Image
				src="/hellomed2.png"
				alt="SA Varejo"
				maxW="120px"
				maxH="50px"
				objectFit="contain"
			/>
			<Flex align="center">
				<Grid
					gridTemplateColumns="1fr 1.5rem"
					alignItems="center"
					height="20"
					gap="2"
				>
					<Box>
						<Text
							textStyle="headerMD"
							color="secondaryText"
							noOfLines={1}
							as="header"
						>
							{user?.name}
						</Text>
						<Text
							textStyle="textXS"
							color="secondaryText"
						>
							Meus dados
						</Text>
					</Box>
					<Menu>
						{({ isOpen: isOpenMenu }) => (
							<>
								<MenuButton>
									<Icon
										as={IoIosArrowDown}
										fontSize="24"
										cursor="pointer"
										color="secondaryText"

									/>
								</MenuButton>
								{isOpenMenu && (
									<MenuList mt="7">
										<MenuItem onClick={() => Router.push(`/${currentRoute}/profile`)}>
											Meus dados
										</MenuItem>
										<MenuItem onClick={() => {
											queryClient.clear()
											signOut()
										}}>
											Sair
										</MenuItem>
									</MenuList>
								)}
							</>
						)}
					</Menu>
				</Grid>
			</Flex>
		</Flex >
	)
}
