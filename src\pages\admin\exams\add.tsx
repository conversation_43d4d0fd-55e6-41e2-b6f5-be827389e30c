import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	Wrap,
	WrapI<PERSON>,
	Stack,
	SimpleGrid,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { Submit<PERSON>and<PERSON>, useForm } from "react-hook-form"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { Input } from "~/components/global/Form/Input"
import { InputImage } from "~/components/global/Form/ImputImage"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { Option } from "~/utils/Types/Global"
import { InputCreatableSelect } from "~/components/global/Form/InputCreatableSelect"

type FormData = {
	name: string
	tags: Option[]
	thumbSecureId: string
}

const FormSchema = yup.object().shape({
	name: yup.string().required("Nome obrigatório"),
	thumbSecureId: yup.string().required("Thumb obrigatória"),
})

interface ExamsAddProps { }

const ExamsAdd: NextPage<ExamsAddProps> = () => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		watch,
		setValue,
		control,
		clearErrors
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
		},
	})

	const add = useMutation(
		async (values: FormData) => {
			return await api.post("/v1/admin/exams", {
				...values,
				tags: values.tags?.map(tag => tag.value).toString(),
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["ExamsAdmin"])
				toast({
					title:
						response.data?.message || "Novo exame cadastrado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao cadastrar exame.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleAdd: SubmitHandler<FormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch { }
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleAdd)}
			>
				<VStack spacing="4" align="flex-start">
					<Wrap w="100%" spacing="8">
						<WrapItem w={{ sm: "100%", xl: "calc(50% - 32px)" }}>
							<Stack spacing="6" w="100%">
								<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
									<Input
										placeholder="Nome *"
										label="Nome *"
										error={errors.name}
										{...register("name")}
									/>
								</SimpleGrid>
								<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
									<InputCreatableSelect
										name="tags"
										label="Tags"
										placeholder="Digite as tags"
										noOptionsMessage={() => ""}
										error={errors.tags as any}
										control={control}
									/>
								</SimpleGrid>
							</Stack>
						</WrapItem>
						<WrapItem w={{ sm: "100%", xl: "calc(50% - 32px)" }}>
							<InputImage
								name="thumbSecureId"
								label="Thumb"
								watch={watch}
								setValue={setValue}
								clearErrors={clearErrors}
								error={errors.thumbSecureId}
							/>
						</WrapItem>
					</Wrap>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Cadastrar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		return {
			props: {},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["exams_create"],
	}
)

export default ExamsAdd
