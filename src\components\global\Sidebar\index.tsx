import {
  useBreakpointValue,
  Drawer,
  Drawer<PERSON><PERSON>lay,
  Drawer<PERSON>ontent,
  DrawerCloseButton,
  DrawerHeader,
  DrawerBody,
  VStack,
} from "@chakra-ui/react";
import { FC } from "react";
import { useSidebarDrawer } from "~/contexts/SideBarDrawerContext";
import { SidebarNav } from "./SidebarNav";

interface SidebarAdminProps {}

export const Sidebar: FC<SidebarAdminProps> = ({}) => {
  const { isOpen, onClose } = useSidebarDrawer();

  const isDrawerSidebar = useBreakpointValue({
    base: false,
    xl: true,
  });

  if (!isDrawerSidebar) {
    return (
      <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
        <DrawerOverlay>
          <DrawerContent bg="secondary" p="4">
            <DrawerCloseButton mt="6" />
            <DrawerHeader>Navegação</DrawerHeader>
            <DrawerBody>
              <SidebarNav onCloseSidebar={onClose} />
            </DrawerBody>
          </DrawerContent>
        </DrawerOverlay>
      </Drawer>
    );
  }

  return (
    <VStack
      as="aside"
      w="16rem"
      height="calc(100vh - 5rem)"
      position="fixed"
      bg="secondary"
      align="flex-start"
    >
      <SidebarNav onCloseSidebar={onClose} />
    </VStack>
  );
};
