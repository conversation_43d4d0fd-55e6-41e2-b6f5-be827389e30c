import {
	Box,
	<PERSON>ton,
	<PERSON>lex,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	Table,
	TableContainer,
	Tbody,
	Text,
	Th,
	The<PERSON>,
	Tr,
	VStack,
	useDisclosure,
} from "@chakra-ui/react"
import { useMemo } from "react"
import { GetServerSideProps, NextPage } from "next"

import { useCan } from "~/hooks/useCan"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { usePartnersAdmin } from "~/hooks/Admin/Partners/usePartnersAdmin"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"
import { CardPartnerAdmin } from "~/components/Admin/Partner/CardPartnerAdmin"
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel"
import { ButtonToModal } from "~/components/global/Buttons/ButtonToModal"
import { ModalImportPartner } from "~/components/Admin/Partner/ModalImportPartner"

interface PartnersProps {

}

const Partners: NextPage<PartnersProps> = () => {
	const { isOpen, onToggle } = useDisclosure()

	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters()

	const { data, isLoading, error, isFetching } = usePartnersAdmin({ page, search, limit })

	const userCanSeeCreate = useCan({
		permissions: ['partners_create']
	})

	const userCanSeeEdit = useCan({
		permissions: ['partners_edit']
	})

	const userCanSeeDelete = useCan({
		permissions: ['partners_delete']
	})

	const dataExportExcel = useMemo(() => {
		if (data) {
			return data.partners.map(partner => ({
				'NOME': partner.name,
				'E-MAIL': partner.email,
				'CPF': partner.legal_document_number,
				'CELULAR': partner.cell,
			}))
		}

		return []
	}, [data])

	return (
		<>
			<VStack spacing="4" layerStyle="container">
				<Flex w="100%" justify="space-between" align="center">
					<Flex>
						<Text
							textStyle="headerLG"
							as="header"
						>
							Parceiros
						</Text>
					</Flex>
					<HStack spacing="4">
						<ButtonExportExcel
							isDisabled={dataExportExcel.length === 0}
							data={dataExportExcel}
							fileName={`exportar_parceiros`}
						>
							Exportar
						</ButtonExportExcel>
						{userCanSeeCreate && (
							<>
								<ButtonToModal
									action={onToggle}
								>
									Importar
								</ButtonToModal>
								<ButtonToCreate linkHref="/admin/partners/add">
									Novo
								</ButtonToCreate>
							</>
						)}
					</HStack>
				</Flex>
				<VStack layerStyle="card" width="100%" p="4">
					<Flex w="100%" justify="space-between">
						<Flex>
							{!!error && (
								<Flex justify="center">
									<Text>Falha ao obter dados.</Text>
								</Flex>
							)}
						</Flex>
						<Stack spacing="4" align="center" direction={{ sm: "column-reverse", lg: "row" }}>
							{isFetching && !isLoading && (
								<Spinner />
							)}
							<Box w="72">
								<InputSearch
									name="search"
									placeholder="Nome, e-mail ou CEP"
									setPage={setPage}
									setSearch={setSearch}
								/>
							</Box>
						</Stack>
					</Flex>
					{data && (
						<>
							<TableContainer w="100%">
								<Table>
									<Thead>
										<Tr>
											<Th>Nome</Th>
											<Th>E-mail</Th>
											<Th>CPF/CNPJ</Th>
											{(userCanSeeEdit || userCanSeeDelete) && (
												<Th>
													<Text
														align="center"
													>
														Ações
													</Text>
												</Th>
											)}
										</Tr>
									</Thead>
									<Tbody>
										{data.partners.map(partner => (
											<CardPartnerAdmin
												key={partner.secure_id}
												partner={partner}
											/>
										))}
									</Tbody>
								</Table>
							</TableContainer>
							<Flex justify="flex-end" w="100%">
								<Pagination
									totalCountOfRegisters={data.total}
									registersInCurrentPage={data.partners.length}
									currentPage={data.page}
									registersPerPage={data.perPage}
									onPageChange={setPage}
									limit={limit}
									setLimit={setLimit}
								/>
							</Flex>
						</>
					)}
				</VStack>
			</VStack>
			{isOpen && (
				<ModalImportPartner
					isOpen={isOpen}
					closeModal={onToggle}
				/>
			)}
		</>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(async (ctx) => {
	return {
		props: {
		}
	}
}, {
	roles: ['MASTER', 'ADMIN'],
	permissions: ['partners_view']
})

export default Partners
