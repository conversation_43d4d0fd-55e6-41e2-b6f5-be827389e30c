import {
	<PERSON><PERSON>,
	Flex,
	HStack,
	SimpleGrid,
	useToast,
	VStack
} from "@chakra-ui/react"
import { FC, useState } from "react"

import { AxiosError } from "axios"
import { useMutation } from "@tanstack/react-query"

import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { api } from "~/services/apiClient"
import { ShowUser } from "~/utils/Types/Admin/User"
import { queryClient } from "~/services/queryClient"
import { PermissionProps } from "~/utils/Types/Permissions"

import { PermissionsCardAdmin } from "./PermissionsCardAdmin"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"

interface FormEditPermissionUserAdminProps {
	permissions: PermissionProps[]
	user: ShowUser
}

type FormData = {
	permissions: any
}

const groupByPermissionsForGroup = function (xs: any, key: any) {
	return xs.reduce(function (rv: any, x: any) {
		(rv[x[key]] = rv[x[key]] || []).push(x)
		return rv
	}, {})
}

export const FormEditPermissionUserAdmin: FC<FormEditPermissionUserAdminProps> = ({ permissions, user }) => {
	const permissionsFormated = Object.values(groupByPermissionsForGroup(permissions, 'group')) as PermissionProps[][]
	const { formState, handleSubmit, setValue } = useForm<FormData>()
	const [checkedItems, setCheckedItems] = useState(permissionsFormated.map(permission => false))
	const [checkedAll, setCheckedAll] = useState<boolean | undefined>(false)
	const toast = useToast()

	const allChecked = checkedItems.every(Boolean)
	const isIndeterminate = checkedItems.some(Boolean) && !allChecked

	const editPermissionsUser = useMutation(async (data: FormData) => {
		await api.put(`/v1/admin/user-permissions/${user.secure_id}`, {
			permissions: Object.values(data.permissions).filter(value => value !== null)
		})
	}, {
		onSuccess: () => {
			queryClient.invalidateQueries(["ActionLogsAdmin", user.secure_id])
			toast({
				title: 'Permissões do usuário atualizadas com sucesso!',
				position: "top-right",
				status: "success",
				isClosable: true,
			})
		},
		onError: (err: AxiosError<any>) => {
			toast({
				title: err?.response?.data?.message || 'Ocorreu um erro ao atualizar as permissões do usuário.',
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	})

	const handleChangePermissions: SubmitHandler<FormData> = async (data) => {
		try {
			await editPermissionsUser.mutateAsync(data)
		} catch {}
	}

	return (
		<VStack
			as="form"
			spacing="8"
			onSubmit={handleSubmit(handleChangePermissions)}
		>
			<Flex
				w="100%"
				justify="flex-start"
				mt="2"
			>
				<Checkbox
					isChecked={allChecked}
					isIndeterminate={isIndeterminate || checkedAll === null}
					size="lg"
					onChange={(e) => {
						setCheckedItems(permissionsFormated.map(permission => e.target.checked))
						if (e.target.checked) {
							setCheckedAll(true)
							permissionsFormated.forEach(permission => permission.forEach(permission => setValue(`permissions.${permission.slug}`, permission.slug)))
						} else {
							setCheckedAll(false)
							permissionsFormated.forEach(permission => permission.forEach(permission => setValue(`permissions.${permission.slug}`, null)))
						}
					}}
				>
					Marcar todas as permissões
				</Checkbox>
			</Flex>
			<SimpleGrid columns={{ sm: 1, lg: 2 }} spacing={10} w="100%">
				{permissionsFormated.map((permissions, index) => (
					<PermissionsCardAdmin
						key={permissions[0].slug}
						name={permissions[0].group}
						permissionsDefault={user.permissions.filter(permission => permission.group === permissions[0].group)}
						permissions={permissions}
						setValue={setValue}
						positionIndexCard={index}
						globalCheckedItems={checkedItems}
						setGlobalCheckedItems={setCheckedItems}
						checkedAll={checkedAll}
						setCheckedAll={setCheckedAll}
					/>
				))}
			</SimpleGrid>
			<Flex justify="flex-end" w="100%">
				<HStack spacing="4" width="20em">
					<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
					<ButtonSubmit isLoading={formState.isSubmitting}>Salvar</ButtonSubmit>
				</HStack>
			</Flex>
		</VStack>
	)
}
