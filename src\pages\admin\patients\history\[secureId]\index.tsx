import { GetServerSideProps, NextPage } from "next";
import { IoMdArrowRoundBack } from "react-icons/io";
import { Accordion, AccordionButton, AccordionIcon, AccordionItem, AccordionPanel, Box, Button, Flex, HStack, Spinner, Table, TableContainer, Tbody, Text, Th, Thead, Tr, useMediaQuery, VStack } from "@chakra-ui/react"

import { setupApiClient } from "~/services/api"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { useRouter } from "next/router";
import { FaFilter } from "react-icons/fa";
import { InputSelect } from "~/components/global/Form/InputSelect";
import { InputSearch } from "~/components/global/Form/InputSearch";
import { useCan } from "~/hooks/useCan";
import { Pagination } from "~/components/global/Pagination";
import { CardPatientHistory } from "~/components/Admin/Patient/History/CardPatientHistory";
import { useControlFilters } from "~/contexts/ControlFiltersContext";
import { useHistory } from "~/hooks/Admin/Patients/useHistory";
import { useForm } from "react-hook-form";
import { ButtonSortTable } from "~/components/global/Buttons/ButtonSortTable";

type Props = {
	patientSecureId: string;
}
// type Props = {
// 	data: PatientHistoryData[];
// 	meta: Meta
// }

type PatientHistoryData = {
	secureId: string;
	status: string;
	specialtyOrExam: string;
	solicitationDate: string;
	
	appointmentSecureId: string;
	appointmentDate: string;

	partner: {
		secureId: string;
		name: string;
	}
}

type Meta = {
	total: number;
	per_page: number;
	current_page: number;
	last_page: number;
	first_page: number;
	first_page_url: string | null;
	last_page_url: string | null;
	next_page_url: string | null;
	previous_page_url: string | null;
}

const PatientHistory: NextPage<Props> = ({ patientSecureId }: Props) => {
	const { page, limit, search, setPage, setLimit, setSearch, direction, field, setField, setDirection, fieldSelect, setFieldSelect } = useControlFilters()

	const {} = useForm(); // HERE

	const { 
		data: historyData, 
		isLoading: isHistoryHookLoading, 
		isFetching: isHistoryHookFetching,
		error: historyError,
	} = useHistory({ secureId: patientSecureId, page, search, limit, direction, field, status: fieldSelect })
	const isLoading = isHistoryHookLoading || isHistoryHookFetching;

	const router = useRouter();

	function handleGoBack() {
		router.back();
	}

	const isTablet = useMediaQuery("(max-width: 768px)")[0];

	const userCanEdit = useCan({
		permissions: ['patients_edit']
	})

	const userCanDelete = useCan({
		permissions: ['patients_delete']
	})

	return (
		<VStack spacing="4" layerStyle="container">
			<Flex w="100%" justify="space-between" align="center">
				<Flex w="fit-content" flexDir='row' alignItems='center' gap={4}>
					<Button bg="none" onClick={handleGoBack}>
						<IoMdArrowRoundBack size={32} color="#3ac1bc" />
					</Button>

					<Text
						textStyle="headerLG"
						as="header"
						minW="220px"
					>
						Histórico de pacientes
					</Text>
				</Flex>

				<Flex w="100%" justify="space-between">
					<Flex>
						{!!historyError && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>

					<HStack spacing="4" align="center">
						{isLoading && (
							<Spinner />
						)}
					</HStack>
				</Flex>
			</Flex>
			<VStack layerStyle="card" width="100%" p="4">
				<Flex justify="space-between" align="center" w="100%">
						<Box>
							<InputSelect
								name="status"
								options={[
									{ label: 'Todos', value: 'all' },
									{ label: 'Aguardando Backoffice', value: 'waiting_backoffice' },
									{ label: "Orçamento", value: "budget" },
									{ label: 'Aguardando Paciente', value: 'waiting_patient' },
									{ label: 'Aprovado', value: 'approved' },
									{ label: 'Não compareceu', value: 'did_not_attend' },
									{ label: 'Realizado', value: 'realized' },
									{ label: 'Finalizado', value: 'finalized' },
									{ label: 'Cancelado pelo Paciente', value: 'canceled_by_Patient' },
									{ label: 'Cancelado a pedido do Paciente', value: 'canceled_at_patient_request' },
									{ label: 'Cancelado pelo Backoffice', value: 'canceled_by_backoffice' },
									{ label: "Em credenciamento", value: "in_accreditation" },
									{ label: "Sem Contato", value: "no_contact" },
									{ label: "Sem Interesse", value: "no_interest" },
									{ label: "Falta Pedido", value: "lack_request" },
									{ label: "Divergência de Informações", value: "info_divergence" },
									{ label: 'Condição Financeira', value: 'financial_condition' },
									{ label: 'Sem Interesse Credenciamento', value: 'no_interest_accreditation' },
								]}
								onChange={event => setFieldSelect(event.target.value)}
								bg="blackAlpha.100"
							/>
						</Box>
						<HStack spacing="4" align="center">
							{isHistoryHookFetching && !isLoading && (
								<Spinner />
							)}
							<Box w={{ sm: '100%', md: "360px" }} alignSelf='flex-end'>
								<InputSearch
									name="search"
									placeholder="Especialidade, exame, médico"
									setPage={setPage}
									setSearch={setSearch}
									value={search}
								/>
							</Box>
						</HStack>
					</Flex>


					{/* <Flex justify="space-between" align="center" w="100%">
						<Accordion w="100%" allowToggle px={0} mx={0}>
							<AccordionItem>
								<AccordionButton px={2}>
									<Box 
										as='span' 
										flex='1' 
										textAlign='left' 
										display='flex' 
										justifyContent='flex-start' 
										alignItems='center'
										gap={4}
									>
										Filtros

										<FaFilter />
									</Box>
									
									<AccordionIcon />
								</AccordionButton>

								<AccordionPanel 
									pb={4} 
									display='flex' 
									flexDirection={isTablet ? 'column' : 'row'}
									justifyContent={isTablet ? 'center' : 'space-between'}
									gap={4}
								>
									<InputSelect
										label="Status"
										options={[
											{ label: 'Todos', value: 'all' },
											{ label: 'Ativo', value: 'active' },
											{ label: 'Inativo', value: 'inactive' }
										]}
										{...filterForm.register('status')}
										isDisabled={isLoading || isFetching}
									/> 
								</AccordionPanel>
							</AccordionItem>
						</Accordion>
					</Flex> */}

					<>
						<TableContainer w="100%">
							<Table>
								<Thead>
									<Tr>
										<Th>Tipo</Th>
										<Th>Data (Consulta)</Th>
										<Th>Data (Agendamento)</Th>
										<Th>Médico</Th>
										<Th>Especialidade/Exame</Th>
										<Th>Status</Th>
										
										{(userCanEdit || userCanDelete) && (
											<Th>
												<Text
													align="center"
												>
													Ações
												</Text>
											</Th>
										)}
									</Tr>
								</Thead>

								<Tbody>
									{historyData?.history.map((history) => (
										<CardPatientHistory 
											key={history.secureId} 
											data={history}
										/>
									))}
								</Tbody>
							</Table>
							
						</TableContainer>

						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={
									historyData?.meta.total ? historyData?.meta.total : 1
								}
								registersInCurrentPage={
									historyData?.history.length ? historyData?.history.length : 1
								}
								currentPage={
									historyData?.meta.current_page ? historyData?.meta.current_page : 1
								}
								registersPerPage={
									historyData?.meta.per_page ? historyData?.meta.per_page : 1
								}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				</VStack>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { secureId } = ctx.query

		return {
			props: {
				patientSecureId: secureId
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["patients_edit"],
	}
)

export default PatientHistory
