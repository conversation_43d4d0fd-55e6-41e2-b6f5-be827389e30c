import {
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC, useCallback, useRef, useState } from "react"

import { RiCalendarCheckLine, RiCalendarLine, RiEyeLine, RiPencilLine } from "react-icons/ri"

import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { SubmitHandler, useForm } from "react-hook-form"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { PopoverComponent } from "~/components/global/Popover"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { addDays, isWeekend } from "date-fns"
import moment from "moment"
import { FormatDateAsHour } from "~/utils/Functions/FormatDates"
import { masks } from "~/utils/Functions/Masks"
import { getStatus } from "~/utils/Functions/AppointmentAndScheduleStatus"
import { formatCurrency } from "~/utils/Functions/formatCurrency"

type FormData = {
	status: string
}

type CardScheduleAndAppointmentsProps = {
	secure_id: string
	createdAt: string
  isAppointment: boolean
	patientName: string
	phoneNumber: number | null
	phoneDDD: number | null
  city: string | null
  state: string | null
  hasAccreditation: boolean
	status: string
  specialtyOrExam?: string
  patientValue: number
  accreditedValue: number
  subsidyValue: number
  hellomedValue: number
}

export const CardScheduleAndAppointments: FC<CardScheduleAndAppointmentsProps> = ({
  secure_id,
  createdAt,
  isAppointment,
  patientName,
  phoneNumber,
  phoneDDD,
  city,
  state,
  hasAccreditation,
  status,
  specialtyOrExam,
  patientValue,
  accreditedValue,
  subsidyValue,
  hellomedValue
}) => {
	const toast = useToast()

	const { formState, handleSubmit, reset, register, watch } = useForm<FormData>({
		defaultValues: {
		}
	})

	const userCanSeeEdit = useCan({
		permissions: ["schedule_edit"]
	})
	const [isManualLoading, setIsManualLoading] = useState<boolean>(false)
	const [isOpenPopover, setIsOpenPopover] = useState(false)
	const firstFieldRef = useRef(null)
			
	function onClosePopover() {
		setIsOpenPopover(false)
		reset()
	}

	function onOpenPopover() {
		setIsOpenPopover(true)
	}

  const formattedPhone = masks('cellPhone', `${phoneDDD}${phoneNumber}`)

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{FormatDateAsHour(createdAt)}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patientName}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{formattedPhone}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{city}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{state}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{hasAccreditation ? 'Credenciamento' : 'Agendamento'}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{isAppointment ? 'Não' : 'Sim'}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{getStatus(status)}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{specialtyOrExam}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patientValue ? formatCurrency(patientValue) : ''}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{accreditedValue ? formatCurrency(accreditedValue) : ''}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{subsidyValue ? formatCurrency(subsidyValue) : ''}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{hellomedValue ? formatCurrency(hellomedValue) : ''}</Text>
			</Td>
			{/* <Td>
				<HStack justify="space-between">
					<Text fontSize="sm">{getStatus(status)}</Text>
					<PopoverComponent
						isOpen={isOpenPopover}
						initialFocusRef={firstFieldRef}
						onOpen={onOpenPopover}
						onClose={onClosePopover}
						title="Alterar status"
						// tooltipLabel="Alterar status"
						placement="auto"
						body={
							<Flex
								as="form"
								flexDirection="column"
								onSubmit={handleSubmit(handleChangeStatus)}
							>
								<InputSelect
									label="Status"
									placeholder="Selecione um status"
									options={[
										{ label: "Cancelado pelo Paciente", value: "canceled_by_patient" },
										{ label: "Cancelado pelo Backoffice", value: "canceled_by_backoffice" },
										{ label: "Cancelado a pedido do Paciente", value: "canceled_at_patient_request" },
										{ label: "Em credenciamento", value: "in_accreditation" },
										{ label: "Sem Contato", value: "no_contact" },
										{ label: "Sem Interesse", value: "no_interest" },
										{ label: "Falta Pedido", value: "lack_request" },
										{ label: "Divergência de Informações", value: "info_divergence" },
										{ label: 'Condição Financeira', value: 'financial_condition' },
										{ label: 'Sem Interesse Credenciamento', value: 'no_interest_accreditation' },
									]}
									{...register("status")}
								/>

								<PopoverFooter display="flex" justifyContent="flex-end" pt="5" mt="2">
									<Flex justify="flex-end" w="100%">
										<HStack spacing="4" width="20em">
											<ButtonCancelSubmit onClick={onClosePopover}>
												Cancelar
											</ButtonCancelSubmit>
											<ButtonSubmit isLoading={formState.isSubmitting} >
												Alterar
											</ButtonSubmit>
										</HStack>
									</Flex>
								</PopoverFooter>
							</Flex>
						}
					>
						<Box>
							{userCanSeeEdit && (
								<PopoverTrigger>
									<IconButton size="sm" aria-label="Edit status" icon={<RiPencilLine />} isDisabled={status === "approved"} />
								</PopoverTrigger>
							)}
						</Box>
					</PopoverComponent>
				</HStack>
			</Td> */}
			{/* {userCanSeeEdit && (
				<Td>
					<Flex justify="flex-end">
						<HStack>
							{["waiting_backoffice", "budget"].includes(status) && (
								<ButtonToEditItemList
									icon={RiCalendarLine}
									tooltipLabel="Gerenciar Agendamento"
									colorScheme="facebook"
									linkHref={`/admin/schedules/${secure_id}`}
								/>
							)}
							{status === "waiting_patient" && (
								<ButtonToEditItemList
									icon={RiCalendarCheckLine}
									tooltipLabel="Aprovar Agendamento"
									colorScheme="facebook"
									linkHref={`/admin/schedules/${secure_id}/approve`}
								/>
							)}
							{!(["waiting_backoffice", "budget", "waiting_patient"].includes(status)) && (
								<ButtonToEditItemList
									icon={RiEyeLine}
									colorScheme="telegram"
									tooltipLabel="Visualizar Solicitação Agendamento"
									linkHref={`/admin/schedules/${secure_id}/view`}
								/>
							)}
						</HStack>
					</Flex>
				</Td>
			)} */}
		</Tr>
	)
}
