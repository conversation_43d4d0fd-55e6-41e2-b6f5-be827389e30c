import {
	Flex,
	HStack,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { ButtonDelete } from "~/components/global/Buttons/ButtonDelete"

type CardDependentAdminProps = {
	secureId: string
	parentSecureId: string;
	name: string
	email: string
	legal_document_number: string
	birth_date: string
}

export const CardDependentPartner: FC<CardDependentAdminProps> = ({ parentSecureId, secureId, name, birth_date, legal_document_number }) => {
	const toast = useToast()

	const deleteUser = useMutation(async () => {
		return await api.delete(`/v1/partner/dependents-partner/${secureId}`)
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['DependentsPartner'])
			toast({
				title: response.data?.message || 'Dependente apagado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao apagar dependente.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{name}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{legal_document_number}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{birth_date}</Text>
			</Td>
				<Td>
					<Flex justify="flex-end">
						<HStack>
								<ButtonToEditItemList
									tooltipLabel="Editar dependente"
									linkHref={`/partner/patients/${parentSecureId}/dependents/${secureId}`}
								/>
                
								<ButtonDelete
									deleteFunction={deleteUser}
									titlePopover="Remover dependente"
									tooltipLabel="Remover dependente"
									message="Deseja remover esse dependente?"
								/>
						</HStack>
					</Flex>
				</Td>
		</Tr>
	)
}
