import { useQuery } from "@tanstack/react-query";
import { api } from "~/services/apiClient";

type GetDependencyCityByStateUFProps = {
	stateUF: string | undefined;
}

type DependenciesGetCityByEstadoUFResponse = {
	cities: { name: string; }[];
}

export type GetDependencyCityByStateUFResponse = {
	cities: { label: string, value: string }[];
}

export async function getDependencyCityByStateUF({ stateUF }: GetDependencyCityByStateUFProps): Promise<GetDependencyCityByStateUFResponse> {
	if (!stateUF || stateUF?.toLocaleLowerCase() === 'selecione um estado') {
		return {} as GetDependencyCityByStateUFResponse;
	}

	const response = await api.get<DependenciesGetCityByEstadoUFResponse>(
		`/v1/admin/appointments-dependencies-get-city/${stateUF}`
	)

	const allCities = [];
	allCities.push(
		{ label: 'Selecione uma cidade', value: 'selecione uma cidade' }
	);

	const formattedCitiesAsSelectOptions = response?.data?.cities?.map((city) => {
		return {
			label: city.name,
			value: city.name
		}
	})

	const formattedCities = [
		...allCities,
		...formattedCitiesAsSelectOptions
	]

	return {
		cities: formattedCities
	}
}

export function useAppointmentsDependenciesGetCityByEstadoUF({ stateUF }: GetDependencyCityByStateUFProps) {
	return useQuery(['AppointmentAdminDependencyGetCityByStateUF', stateUF], () => getDependencyCityByStateUF({ stateUF: stateUF }))
}
