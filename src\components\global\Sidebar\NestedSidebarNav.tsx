import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
  Box,
  Icon,
  Text,
} from "@chakra-ui/react";
import React, { useMemo, useState } from "react";
import { IconType } from "react-icons";
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from "react-icons/md";
import { useCan } from "~/hooks/useCan";
import { PERMISSIONS } from "~/utils/Types/Permissions";

interface SubmenuProps {
  icon: IconType;
  sizeIcon: number;
  permissionLink?: PERMISSIONS;
  children: React.ReactNode;
  text: string;
}

export function Submenu({
  children,
  icon,
  sizeIcon,
  permissionLink,
  text,
}: SubmenuProps) {
  const [isHovered, setIsHovered] = useState(false);

	let userHasValidPermission = true

	userHasValidPermission = useCan({ permissions: permissionLink ? [permissionLink] : undefined })

  return (
    <Accordion allowToggle w="full">
      <AccordionItem border="none" w="full" borderRadius="10" mx={0} px={0}>
        {({ isExpanded }) => (
          <>
            <AccordionButton
              w="full"
              // p="15px"
							px={2}
              borderRadius="10"
              display={userHasValidPermission ? "flex" : "none"}
              _hover={{
								textDecoration: 'none',
								bg: 'blackAlpha.300'
							}}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              <Icon
                as={icon}
                fontSize={sizeIcon}
								color="white"
              />
              <Text
                ml="4"
                fontWeight="medium"
                textTransform="capitalize"
								color="white"
              >
                {text}
              </Text>

              <Box display="flex" justifyContent="end" w="full">
                {isExpanded ? (
                  <MdKeyboardArrowUp size={20} color="white" />
                ) : (
                  <MdKeyboardArrowDown size={20} color="white" />
                )}
              </Box>
            </AccordionButton>

            <AccordionPanel m={0} my={2} p={0} pl={4}>{children}</AccordionPanel>
          </>
        )}
      </AccordionItem>
    </Accordion>
  );
}
