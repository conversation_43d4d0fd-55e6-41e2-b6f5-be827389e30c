import { ActionLog } from "../Global";
import { PermissionProps } from "../Permissions"

export type ShowUser = {
  secure_id: string
  email: string
  userInfo: {
    name: string
    legal_document_number: string;
    ddd_cell: string;
    cell: string;
    zip_code: string;
    street: string;
    number: string;
    complement: string;
    neighborhood: string;
    city: string;
    state: string;
    birth_date: string
    origin: string
  }
  avatar?: {
    secure_id: string
    url: string
    name: string
  }
  permissions: PermissionProps[]
  logs?: ActionLog[]

  isActive: boolean
}

export type UserFormData = {
  name: string
  email: string
  legal_document_number: string;
  cell: string
  zip_code: string;
  street: string;
  number: string;
  complement: string;
  neighborhood: string;
  city: string;
  state: string;
  birth_date: string
  password: string
  passwordConfirmation: string
  avatarSecureId: string
  userExists: boolean
  userSecureId: string
}
