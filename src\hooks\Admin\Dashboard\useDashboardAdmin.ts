import { useQuery } from "@tanstack/react-query"

import ptBR from 'date-fns/locale/pt-BR'
import { add, differenceInHours, formatDistanceToNowStrict } from "date-fns"

import { api } from "~/services/apiClient"
import { getAppointmentStatus } from "~/utils/Types/Global"
import { InCredentialSchedulesDashboard, ListAppointmentDashboard, ResponseAppointmentDashboard } from "~/utils/Types/Admin/Dashboard"

export type GetDashboardResponse = {
	appointments: Appointments;
	inCredentialSchedules: InCredentialSchedules;
}

type Appointments = {
	meta: {
		total: number
		page: number
		lastPage: number
		perPage: number
	}

	data: ListAppointmentDashboard[]
}

type InCredentialSchedules = {
	meta: {
		total: number
		page: number
		lastPage: number
		perPage: number
	}

	data: InCredentialSchedulesDashboard[]
}

type GetDashboardProps = {
	page: number
	limit?: number
	startDate?: string
	endDate?: string
	specialty?: string
	exam?: string
	partner?: string

	followUpDateStart?: string
	followUpDateEnd?: string

	inCredentialedSchedulesPage: number;
	inCredentialedSchedulesLimit?: number;
	inCredentialedSchedulesName?: string;
}

export async function getDashboardAdmin({ 
	page, 
	limit, 
	startDate, 
	endDate, 
	specialty, 
	exam, 
	partner,
	followUpDateStart,
	followUpDateEnd,

	inCredentialedSchedulesPage,
	inCredentialedSchedulesLimit,
	inCredentialedSchedulesName,
 }: GetDashboardProps): Promise<GetDashboardResponse> {
	const response = await api.get('/v1/admin/dashboard', {
		params: {
			page,
			limit,
			startDate,
			endDate,
			specialty,
			exam,
			partner,
			followUpDateStart,
			followUpDateEnd,

			inCredentialedSchedulesPage,
			inCredentialedSchedulesLimit,
			inCredentialedSchedulesName,
		}
	})


	const appointments = response.data.appointments.data.map((appointment: ResponseAppointmentDashboard) => {
		const startDate = new Date(appointment.created_at)
		const expirationDate = add(startDate, { hours: 72 })

		const now = new Date()
		const hoursRemaining = differenceInHours(expirationDate, now)

		const expiration_time = hoursRemaining > 0 && hoursRemaining <= 72
			? { type: hoursRemaining > 24 ? 'ok' : hoursRemaining > 15 ? 'warning' : 'danger', message: `${hoursRemaining}h` }
			: hoursRemaining > 72
				? { type: 'ok', message: formatDistanceToNowStrict(expirationDate, { addSuffix: true, locale: ptBR }) }
				: { type: 'danger', message: formatDistanceToNowStrict(expirationDate, { addSuffix: true, locale: ptBR }) }

		return {
			secure_id: appointment.secure_id,
			patient: appointment.patient.userInfo.name,
			partner: appointment.patient.partners[0] ? appointment.patient.partners[0].userInfo.name : '',
			status: getAppointmentStatus(appointment.status),
			expiration_time
		}
	})

	return {
		appointments: {
			meta: {
				total: response.data.appointments.meta.total,
				perPage: response.data.appointments.meta.per_page,
				page: response.data.appointments.meta.current_page,
				lastPage: response.data.appointments.meta.last_page,
			},

			data: appointments
		},

		inCredentialSchedules: {
			meta: {
				total: response.data.inCredentialedSchedules.meta.total,
				perPage: response.data.inCredentialedSchedules.meta.per_page,
				page: response.data.inCredentialedSchedules.meta.current_page,
				lastPage: response.data.inCredentialedSchedules.meta.last_page,
			},

			data: response.data.inCredentialedSchedules.data
		}
	}
}

export function useDashboardAdmin({ 
	page, 
	limit, 
	startDate, 
	endDate, 
	specialty, 
	exam, 
	partner,
	followUpDateStart,
	followUpDateEnd,

	inCredentialedSchedulesPage,
	inCredentialedSchedulesLimit,
	inCredentialedSchedulesName

 }: GetDashboardProps) {
	return useQuery(
		[
			'DashboardAdmin', 
			page, 
			limit, 
			startDate, 
			endDate, 
			specialty, 
			exam, 
			partner,
			followUpDateStart,
			followUpDateEnd,

			inCredentialedSchedulesPage,
			inCredentialedSchedulesLimit,
			inCredentialedSchedulesName
		], () => getDashboardAdmin({ 
			page, 
			limit, 
			startDate, 
			endDate, 
			specialty, 
			exam, 
			partner,
			followUpDateStart,
			followUpDateEnd,

			inCredentialedSchedulesPage,
			inCredentialedSchedulesLimit,
			inCredentialedSchedulesName
		}))
}
