import {
  Box,
  Flex,
  HStack,
  SimpleGrid,
  Text,
  VStack,
  useToast,
} from "@chakra-ui/react";
import { AxiosError, AxiosResponse } from "axios";
import { GetServerSideProps } from "next";

import { yupResolver } from "@hookform/resolvers/yup";
import { useMutation } from "@tanstack/react-query";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import * as yup from "yup";

import { api } from "~/services/apiClient";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";

import dynamic from "next/dynamic";
import { useEffect } from "react";
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit";
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit";
import { Input } from "~/components/global/Form/Input";
import { InputSelect } from "~/components/global/Form/InputSelect";
import { useNotifications } from "~/hooks/Admin/Notifications/useNotifications";
import {
  NOTIFICATIONS_LIST,
  NOTIFICATIONS_TYPE_LIST,
} from "~/utils/constants/notifications";

const InputDraft = dynamic(
  () => import("~/components/global/Form/InputDraft").then((e) => e.InputDraft),
  {
    ssr: false,
  }
);

type FormData = {
  subject: string;
  content: string;
  notification: string;
  email: string;
  notification_type: "email" | "push" | "sms";
};

const FormSchema = yup.object().shape({
  notification: yup.string().required("Notificação obrigatório"),
  notification_type: yup
    .mixed()
    .oneOf(["email", "push", "sms"])
    .required("Tipo de notificação obrigatório"),
  subject: yup.string().optional(),
  content: yup.string().optional(),
  email: yup.string().optional().when("notification", {
    is: (value: string) => (value === 'examCancelForPatientSendBackoffice' || value === 'consultCancelForPatientSendBackoffice'),
    then: (schema) => schema.email('E-mail inválido').required("E-mail obrigatório"),
  }),
});

function Notifications() {
  const toast = useToast();
  const {
    register,
    formState,
    formState: { errors },
    handleSubmit,
    watch,
    setValue,
  } = useForm<FormData>({
    //@ts-ignore
    resolver: yupResolver(FormSchema),
    defaultValues: {},
  });

  const NotificationType = watch("notification_type");
  const NotificationName = watch("notification");

  const { data } = useNotifications({
    type: !!NotificationType ? NotificationType : "sms",
    name: !!NotificationName ? NotificationName : "consultConfirmation",
  });

  useEffect(() => {
    if (NotificationName === 'examCancelForPatientSendBackoffice' || NotificationName === 'consultCancelForPatientSendBackoffice') {
      setValue('notification_type', 'email')
    }
  }, [NotificationName])

  useEffect(() => {
    if (!!data?.notifications.length) {
      const { content, subject } = data.notifications[0];
      setValue("content", content ? content : "");
      setValue("subject", subject ? subject : "");
    }
  }, [data, setValue]);

  const updateNotification = useMutation(async (values: FormData) => {
    return await api.put(
      `/v1/admin/template-notifications/${data?.notifications[0].secure_id}`,
      {
        subject: values?.subject,
        content: values?.content,
        email: values?.email,
      }
    );
  },
    {
      onSuccess: (response: AxiosResponse) => {
        toast({
          title:
            response.data?.message || "Notificação atualizada com sucesso!",
          position: "top-right",
          status: response.data?.type || "success",
          isClosable: true,
        });
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title:
            error?.response?.data?.message ||
            "Ocorreu um problema ao atualizar notificação.",
          position: "top-right",
          status: error?.response?.data?.type || "error",
          isClosable: true,
        });
      },
    }
  );

  const handleUpdate: SubmitHandler<FormData> = async (values) => {
    try {
      await updateNotification.mutateAsync(values);
    } catch (error) { }
  };

  return (
    <VStack spacing="4" layerStyle="container">
      <Flex w="100%" justify="space-between" align="center">
        <Text textStyle="headerLG" as="header">
          Atualizar Notificações
        </Text>
      </Flex>
      <Box
        p="4"
        as="form"
        width="100%"
        layerStyle="card"
        onSubmit={handleSubmit(handleUpdate)}
      >
        <VStack spacing="4" align="flex-start">
          <SimpleGrid w="100%" spacing={6} minChildWidth="180px">
            <InputSelect
              label="Notificações"
              options={NOTIFICATIONS_LIST}
              {...register("notification")}
            />
          </SimpleGrid>

          {(NotificationName === 'examCancelForPatientSendBackoffice' || NotificationName === 'consultCancelForPatientSendBackoffice') ? (
            <Input
              placeholder="E-mail *"
              label="E-mail *"
              type="email"
              error={errors.email}
              {...register("email")}
            />
          ) : (
            <SimpleGrid w="100%" spacing={6} minChildWidth="180px">
              <InputSelect
                label="Tipo de notificação"
                options={NOTIFICATIONS_TYPE_LIST}
                {...register("notification_type")}
              />
            </SimpleGrid>
          )}
          {NotificationType !== "sms" && (
            <SimpleGrid w="100%" spacing={6} minChildWidth="180px">
              <Input
                placeholder={
                  NotificationType === "push" ? "Subtítulo" : "Assunto"
                }
                label={NotificationType === "push" ? "Subtítulo" : "Assunto"}
                error={errors.subject}
                {...register("subject")}
              />
            </SimpleGrid>
          )}
          <SimpleGrid w="100%" spacing={6} minChildWidth="180px">
            <Text fontSize="16px">
              {`Variáveis: {{nomePaciente}} {{nomeMedico}} {{dataConsulta}}
              {{dataPagamento}} {{valorPago}} {{dataRealizacao}}
              {{dataCancelamento}} {{local}} {{especialidade}}
              {{laboratorio}} {{exam}} {{examsPay}} {{preparations}}`}
            </Text>
          </SimpleGrid>
          <SimpleGrid w="100%" spacing={6} minChildWidth="180px">
            {NotificationType !== "email" && (
              <Input
                placeholder="Mensagem"
                label="Conteúdo"
                error={errors.content}
                {...register("content")}
              />
            )}
            {NotificationType === "email" && (
              //@ts-ignore
              <InputDraft
                label="Conteúdo"
                name="content"
                setValue={setValue}
                initialTemplate={
                  data?.notifications[0].content
                    ? data?.notifications[0].content
                    : ""
                }
              />
            )}
          </SimpleGrid>

          <Flex justify="center" w="100%">
            <HStack spacing="50" width="30em">
              <ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
              <ButtonSubmit isLoading={formState.isSubmitting}>
                Atualizar
              </ButtonSubmit>
            </HStack>
          </Flex>
        </VStack>
      </Box>
    </VStack>
  );
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
  async (ctx) => {
    return {
      props: {},
    };
  },
  {
    roles: ["MASTER", "ADMIN"],
    permissions: ["templatenotification_create"],
  }
);

export default Notifications;
