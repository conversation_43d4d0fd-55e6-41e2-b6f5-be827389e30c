import { api } from "~/services/apiClient";
import { useQuery } from "@tanstack/react-query";

export type GetObservationsByScheduleSecureIdProps = {
	appointmentSecureId: string
}

type GetObservationApiResponseDTO = {
	observations: {
		secureId: string;
		observation: string;
	}[]
}
// type GetObservationApiResponseDTO = {
// 	appointmentObservations: {
// 		secure_id: string;
// 		schedule: {
// 			secure_id: string;
// 			observations: {
// 					secureId: string,
// 					observation: string;
// 					created_at: Date;
// 			}[];
// 		}
// 	}
// }

type GetObservationResponse = {
	observations: {
		secureId: string;
		observation: string;
	}[]
}
// type GetObservationResponse = {
// 	scheduleSecureId: string;
// 	observations: {
// 		secureId: string;
// 		observation: string;
// 	}[]
// }

export async function getObservationByScheduleSecureId({ 
	appointmentSecureId 
}: GetObservationsByScheduleSecureIdProps): Promise<GetObservationResponse> {
	// const response = await api.get<GetObservationApiResponseDTO>(
	// 	`/v1/admin/appointments-observations/${appointmentSecureId}`
	// )
	const response = await api.get<GetObservationApiResponseDTO>(
		`/v1/admin/observations/by-schedule-secure-id/${appointmentSecureId}`
	)

	// const scheduleSecureId = response.data.appointmentObservations.schedule.secure_id;
	// const observations = response.data.appointmentObservations.schedule.observations.map((observation) => {
	// 	return {
	// 		secureId: observation.secureId,
	// 		observation: observation.observation
	// 	}
	// })

	return {
		observations: response.data.observations
	}
}

export function useAppointmentObservations(appointmentSecureId: string) {
	return useQuery(['AppointmentObservations', appointmentSecureId], () => getObservationByScheduleSecureId({ appointmentSecureId }))
}
