export type ScheduleDatesRequestsProps = {
  date: string;
  date_type: string;
  type: "patient" | "backoffice";
  payment_methods?: string;
  query_value?: number;
  query_value_subsidy?: number;
  query_value_patient?: number;
  partner: {
    userInfo: {
      name: string;
      ddd_phone: string;
      phone: string;
      ddd_cell: string;
      cell: string;
      status: string;
    };
  };
  partner_type: string;
  secure_id: string;
  status: 'to_check' | 'available' | 'unavailable'
  value?: "morning" | "afternoon" | "night";
};

export type ScheduleShowProps = {
  secure_id: string;
  type_consult: "in_person" | "video_call" | "exam";
  statusDisponibility: 'waiting_backoffice' | 'budget' | 'waiting_patient' | 'approved' | 'canceled_by_patient' | 'canceled_at_patient_request' | 'canceled_by_backoffice'
  status: 'active' | 'inactive' | 'punctual'
  neighborhood: string;
  city: string;
  state: string;
  payment_methods?: string;
  query_value?: number;
  patient: {
    userInfo: {
      name: string;
      ddd_phone: string;
      phone: string;
      ddd_cell: string;
      cell: string;
    };
    parent: {
      userInfo: {
        name: string;
        ddd_phone: string;
        phone: string;
        ddd_cell: string;
        cell: string;
      };
    };
  };
  specialty?: {
    name: string;
  };
  exam?: {
    name: string;
  };
  scheduleDatesRequests: ScheduleDatesRequestsProps[];
  uploads?: {
    url: string;
  }[]

  observations: {
    secureId: string;
    observation: string;
  }[];
};

export type ListScheduleAdmin = {
  secure_id: string;
  type_consult: "in_person" | "video_call" | "exam";
  current_status: 'open' | 'closed'
  status: 'waiting_backoffice' | 'budget' | 'waiting_patient' | 'approved' | 'canceled_by_patient' | 'canceled_at_patient_request' | 'canceled_by_backoffice'
  specialty?: {
    name: string;
  };
  exam?: {
    name: string;
  };
  patient: {
    userInfo: {
      name: string;
    };
  };
  created_at: string;
  type_canceled: 'patient' | 'backoffice';
  city: string;

  followUpDate: string;
};
