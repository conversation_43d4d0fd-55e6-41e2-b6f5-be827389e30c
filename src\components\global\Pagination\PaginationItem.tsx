import { Button } from "@chakra-ui/react"
import { useRouter } from "next/router"

interface PaginationItemProps {
	number: number
	isCurrent?: boolean
	onPageChange: (page: number) => void
	notTop?: boolean
}

export function PaginationItem({
	isCurrent = false,
	number,
	onPageChange,
	notTop = false
}: PaginationItemProps) {
	const router = useRouter()

	if (isCurrent) {
		return (
			<Button
				size="sm"
				fontSize="xs"
				w="4"
				colorScheme="teal"
				disabled
				_disabled={{
					colorScheme: 'teal',
					cursor: 'default'
				}}
			>
				{number}
			</Button>
		)
	}

	return (
		<Button
			size="sm"
			fontSize="xs"
			w="4"
			colorScheme="gray"
			onClick={() => {
				onPageChange(number)
				// if (!notTop) router.push(`${router.pathname}#top`)
			}}
		>
			{number}
		</Button>
	)
}
