import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { ActionLog } from "~/utils/Types/Global"

type GetExamsResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	logs: ActionLog[]
}

type GetExamsProps = {
	changedSecureId: string
	type: 'user' | 'specialty' | 'exam' | 'appointment' | 'schedule' | 'group'
	page: number
	limit?: number
}

export async function getActionLogsAdmin({ changedSecureId, type, page, limit }: GetExamsProps): Promise<GetExamsResponse> {
	const response = await api.get('/v1/admin/action-logs', {
		params: {
			changedSecureId,
			type,
			page,
			limit,
		}
	})

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		logs: response.data.data
	}
}

export function useActionLogsAdmin({ changedSecureId, type, page, limit }: GetExamsProps) {
	return useQuery(['ActionLogsAdmin', changedSecureId, type, page, limit], () => getActionLogsAdmin({ changedSecureId, type, page, limit }))
}
