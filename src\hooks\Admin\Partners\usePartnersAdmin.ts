import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { FormatDateForDayMonthYearUsingBars } from "~/utils/Functions/FormatDates"
import { masks } from "~/utils/Functions/Masks"
import { ListUserAdmin } from "~/utils/Types/Admin/User"

type GetDoctorsResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	partners: ListUserAdmin[]
}

type GetPartnersProps = {
	page: number
	search?: string
	limit?: number
}

export async function getPartnersAdmin({ page, search, limit }: GetPartnersProps): Promise<GetDoctorsResponse> {
	const response = await api.get('/v1/admin/partners', {
		params: {
			search,
			page,
			limit,
		}
	})

	const partners = response.data.data.map((user: any) => ({
		secure_id: user.secure_id,
		name: user.userInfo.name,
		email: user.email,
		legal_document_number: user.userInfo.legal_document_number,
		cell: masks('cellPhone', `${user.userInfo.ddd_cell}${user.userInfo.cell}`),
		type: user.type,
		show_accredited_in_app: user.show_accredited_in_app
	}))

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		partners
	}
}

export function usePartnersAdmin({ page, search, limit }: GetPartnersProps) {
	return useQuery(['PartnersAdmin', page, search, limit], () => getPartnersAdmin({ page, search, limit }))
}
