import {
	Flex,
	<PERSON><PERSON><PERSON>ck,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC } from "react"

import { useCan } from "~/hooks/useCan"
import { ListGroupAdmin } from "~/utils/Types/Admin/Group"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { useMutation } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { AxiosError, AxiosResponse } from "axios"
import { ButtonDelete } from "~/components/global/Buttons/ButtonDelete"

type CardGroupAdminProps = {
	group: ListGroupAdmin
}

export const CardGroupAdmin: FC<CardGroupAdminProps> = ({ group }) => {
	const toast = useToast()

	const userCanSeeEdit = useCan({
		permissions: ['groups_edit']
	})

	const userCanSeeDelete = useCan({
		permissions: ['groups_delete']
	})

	const deleteGroup = useMutation(
		async () => {
			return await api.delete(`/v1/admin/groups/${group.secure_id}`)
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["GroupsAdmin"])
				toast({
					title: response.data?.message || "Grupo removido com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title: error?.response?.data?.message || "Erro ao remover grupo.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{group.name}</Text>
			</Td>
			{(userCanSeeEdit || userCanSeeDelete) && (
				<Td>
					<Flex justify="flex-end">
						<HStack>
							{userCanSeeEdit && (
								<ButtonToEditItemList
									tooltipLabel="Editar grupo"
									linkHref={`/admin/groups/${group.secure_id}`}
								/>
							)}

							{userCanSeeDelete && (
								<ButtonDelete
									deleteFunction={deleteGroup}
									titlePopover="Remover grupo"
									tooltipLabel="Remover grupo"
									message="Deseja remover esse grupo?"
								/>
							)}
						</HStack>
					</Flex>
				</Td>
			)}
		</Tr>
	)
}
