import {
	<PERSON>,
	<PERSON>lex,
	<PERSON><PERSON><PERSON><PERSON>,
	Spinner,
	<PERSON>,
	TableContainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack
} from "@chakra-ui/react"

import { useCan } from "~/hooks/useCan"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useDependentsAdmin } from "~/hooks/Admin/Dependents/useDependentsAdmin"

import { Pagination } from "~/components/global/Pagination"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { ButtonToCreate } from "~/components/global/Buttons/ButtonToCreate"
import { CardDependentAdmin } from "./CardDependentAdmin"

interface TabDependentsProps {
	parent: string
}

export function TabDependents({ parent }: TabDependentsProps) {
	const { page, limit, search, setPage, setLimit, setSearch } = useControlFilters()

	const { data, isLoading, error, isFetching } = useDependentsAdmin({ page, search, limit, parent })

	const userCanSeeCreate = useCan({
		permissions: ['patients_create']
	})

	const userCanSeeEdit = useCan({
		permissions: ['patients_edit']
	})

	const userCanSeeDelete = useCan({
		permissions: ['patients_delete']
	})

	return (
		<VStack spacing="4">
			<VStack width="100%" p="4" align="flex-end">
				{userCanSeeCreate && (
					<ButtonToCreate linkHref={`/admin/patients/${parent}/dependents/add`}>
						Novo
					</ButtonToCreate>
				)}
				<Flex w="100%" justify="space-between">
					<Flex>
						{!!error && (
							<Flex justify="center">
								<Text>Falha ao obter dados.</Text>
							</Flex>
						)}
					</Flex>
					<HStack spacing="4" align="center">
						{isFetching && !isLoading && (
							<Spinner />
						)}
						<Box w="72">
							<InputSearch
								name="search"
								placeholder="Nome"
								setPage={setPage}
								setSearch={setSearch}
							/>
						</Box>
					</HStack>
				</Flex>
				{data && (
					<>
						<TableContainer w="100%">
							<Table>
								<Thead>
									<Tr>
										<Th>Nome</Th>
										<Th>CPF</Th>
										<Th>Data de nascimento</Th>
										{(userCanSeeEdit || userCanSeeDelete) && (
											<Th>
												<Text
													align="center"
												>
													Ações
												</Text>
											</Th>
										)}
									</Tr>
								</Thead>
								<Tbody>
									{data.dependents.map(dependent => (
										<CardDependentAdmin
											key={dependent.secure_id}
											parentSecureId={parent}
											secureId={dependent.secure_id}
											name={dependent.name}
											email={dependent.email}
											birth_date={dependent.birth_date}
											legal_document_number={dependent.legal_document_number}
										/>
									))}
								</Tbody>
							</Table>
						</TableContainer>
						<Flex justify="flex-end" w="100%">
							<Pagination
								totalCountOfRegisters={data.total}
								registersInCurrentPage={data.dependents.length}
								currentPage={data.page}
								registersPerPage={data.perPage}
								onPageChange={setPage}
								limit={limit}
								setLimit={setLimit}
							/>
						</Flex>
					</>
				)}
			</VStack>
		</VStack>
	)
}
