import {
  createContext,
  Dispatch,
  ReactNode,
  SetStateAction,
  useContext,
  useEffect,
  useState,
} from "react";

import { useRouter } from "next/router";
import { destroyCookie, parseCookies, setCookie } from "nookies";
import { Room } from "twilio-video";

interface VideoCallAuth {
  token: string;
  roomName: string;
  userName: string;
}

type VideoAuthContextData = {
  setVideoCredentials: Dispatch<SetStateAction<VideoCallAuth>>;
  videoToken: string;
  roomName: string;
  userName: string;
  roomContext: Room | null;
  setRoomContext: Dispatch<SetStateAction<Room | null>>
};

type VideoAuthProviderProps = {
  children: ReactNode;
};

const VideoCallAuthContext = createContext({} as VideoAuthContextData);

export function removeCookies() {
  destroyCookie(undefined, "@HelloMedVideoCall:token", {
    path: "/",
  });
  destroyCookie(undefined, "@HelloMedVideoCall:roomName", {
    path: "/",
  });
  destroyCookie(undefined, "@HelloMedVideoCall:userName", {
    path: "/",
  });
}

export function cancelCall() {
  removeCookies();
}

export function VideoCallAuthProvider({ children }: VideoAuthProviderProps) {
  const router = useRouter();
  const [videoCredentials, setVideoCredentials] = useState<VideoCallAuth>({
    token: "",
    roomName: "",
    userName: "",
  });
  const [roomContext, setRoomContext] = useState<Room | null>(null);

  useEffect(() => {
    const { "@HelloMedVideoCall:token": videoToken } = parseCookies();
    const { "@HelloMedVideoCall:roomName": roomName } = parseCookies();
    const { "@HelloMedVideoCall:userName": userName } = parseCookies();
    const cookiesConfig = {
      maxAge: 86400000, // 1 day
      path: "/",
    };
    if (!!videoCredentials.token) {
      setCookie(
        undefined,
        "@HelloMedVideoCall:token",
        videoCredentials.token,
        cookiesConfig
      );
      setCookie(
        undefined,
        "@HelloMedVideoCall:roomName",
        videoCredentials.roomName,
        cookiesConfig
      );
      setCookie(
        undefined,
        "@HelloMedVideoCall:userName",
        videoCredentials.userName,
        cookiesConfig
      );
    } else {
      setVideoCredentials({ token: videoToken, roomName, userName: userName });
    }
  }, [router.asPath]);

  return (
    <VideoCallAuthContext.Provider
      value={{
        videoToken: videoCredentials.token,
        roomName: videoCredentials.roomName,
        userName: videoCredentials.userName,
        roomContext,
        setVideoCredentials,
        setRoomContext
      }}
    >
      {children}
    </VideoCallAuthContext.Provider>
  );
}

export function useVideoCallAuthContext() {
  return useContext(VideoCallAuthContext);
}
