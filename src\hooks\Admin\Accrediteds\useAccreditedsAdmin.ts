import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { masks } from "~/utils/Functions/Masks"
import { ListUserAdmin } from "~/utils/Types/Admin/User"

type GetDoctorsResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	accrediteds: ListUserAdmin[]
}

type GetAccreditedsProps = {
	page: number
	search?: string
	limit?: number

	status?: 'active' | 'inactive' | 'punctual' | 'all'
}

export async function getAccreditedsAdmin({ page, search, limit, status }: GetAccreditedsProps): Promise<GetDoctorsResponse> {
	const formattedStatus = status === 'all' ? undefined : status;

	const response = await api.get('/v1/admin/accrediteds', {
		params: {
			search,
			page,
			limit,
			status: formattedStatus
		}
	})


	const accrediteds = response.data.data.map((user: any) => ({
		secure_id: user.secure_id,
		name: user.userInfo.name,
		email: user.email,
		legal_document_number: user.userInfo.legal_document_number,
		cell: masks('cellPhone', `${user.userInfo.ddd_cell}${user.userInfo.cell}`),
		type: user.type,
		show_accredited_in_app: user.show_accredited_in_app,
		status: user.userInfo.status,
	}))

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		accrediteds
	}
}

export function useAccreditedsAdmin({ page, search, limit, status }: GetAccreditedsProps) {
	return useQuery(['AccreditedsAdmin', page, search, limit, status], () => getAccreditedsAdmin({ page, search, limit, status }))
}
