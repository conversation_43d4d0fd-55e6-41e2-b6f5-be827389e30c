import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	Stack,
	SimpleGrid,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { Input } from "~/components/global/Form/Input"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"

type FormData = {
	name: string
}

const FormSchema = yup.object().shape({
	name: yup.string().required("Nome obrigatório"),
})

interface GroupsAddProps {}

const GroupsAdd: NextPage<GroupsAddProps> = () => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
		},
	})

	const add = useMutation(
		async (values: FormData) => {
			return await api.post("/v1/admin/groups", {
				...values,
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["GroupsAdmin"])
				toast({
					title:
						response.data?.message || "Novo grupo cadastrado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao cadastrar grupo.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleAdd: SubmitHandler<FormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch {}
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleAdd)}
			>
				<VStack spacing="4" align="flex-start">
					<Stack spacing="6" w="100%">
						<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
							<Input
								placeholder="Nome *"
								label="Nome *"
								error={errors.name}
								{...register("name")}
							/>
						</SimpleGrid>
					</Stack>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Cadastrar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		return {
			props: {},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["groups_create"],
	}
)

export default GroupsAdd
