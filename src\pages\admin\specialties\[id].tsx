import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	Wrap,
	WrapI<PERSON>,
	<PERSON>ack,
	SimpleGrid,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { Submit<PERSON><PERSON><PERSON>, useForm } from "react-hook-form"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { Option } from "~/utils/Types/Global"
import { setupApiClient } from "~/services/api"
import { queryClient } from "~/services/queryClient"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { Input } from "~/components/global/Form/Input"
import { InputImage } from "~/components/global/Form/ImputImage"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { InputCreatableSelect } from "~/components/global/Form/InputCreatableSelect"

type Specialty = {
	secure_id: string
	name: string
	tags: string
	label_advice: string
	thumb: {
		secure_id: string
		name: string
		url: string
	}
}

type FormData = {
	name: string
	tags: Option[]
	labelAdvice: string
	thumbSecureId: string
}

const FormSchema = yup.object().shape({
	name: yup.string().required("Nome obrigatório"),
	labelAdvice: yup.string().required("Conselho obrigatório"),
	thumbSecureId: yup.string().required("Thumb obrigatória"),
})

interface SpecialtiesEditProps {
	specialty: Specialty
}

const SpecialtiesEdit: NextPage<SpecialtiesEditProps> = ({ specialty }) => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		watch,
		setValue,
		control,
		clearErrors
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			labelAdvice: specialty.label_advice,
			name: specialty.name,
			tags: specialty.tags ? specialty.tags.split(",").map(tag => ({ label: tag, value: tag })) : [],
			thumbSecureId: specialty.thumb.secure_id
		},
	})

	const edit = useMutation(
		async (values: FormData) => {
			return await api.put(`/v1/admin/specialties/${specialty.secure_id}`, {
				...values,
				tags: values.tags.map(tag => tag.value).toString(),
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["SpecialtiesAdmin"])
				queryClient.invalidateQueries(["ActionLogsAdmin", specialty.secure_id])
				toast({
					title:
						response.data?.message || "Especialidade alterada com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao alterar especialidade.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleEdit: SubmitHandler<FormData> = async (values) => {
		try {
			await edit.mutateAsync(values)
		} catch {}
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleEdit)}
			>
				<VStack spacing="4" align="flex-start">
					<Wrap w="100%" spacing="8">
						<WrapItem w={{ sm: "100%", xl: "calc(50% - 32px)" }}>
							<Stack spacing="6" w="100%">
								<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
									<Input
										placeholder="Nome *"
										label="Nome *"
										error={errors.name}
										{...register("name")}
									/>
								</SimpleGrid>

								<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
									<Input
										placeholder="Conselho *"
										label="Conselho *"
										error={errors.labelAdvice}
										{...register("labelAdvice")}
									/>
								</SimpleGrid>
								<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
									<InputCreatableSelect
										name="tags"
										label="Tags"
										placeholder="Digite as tags"
										noOptionsMessage={() => ""}
										error={errors.tags as any}
										control={control}
									/>
								</SimpleGrid>
							</Stack>
						</WrapItem>
						<WrapItem w={{ sm: "100%", xl: "calc(50% - 32px)" }}>
							<InputImage
								name="thumbSecureId"
								label="Thumb"
								watch={watch}
								setValue={setValue}
								clearErrors={clearErrors}
								error={errors.thumbSecureId}
							/>
						</WrapItem>
					</Wrap>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Salvar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
			<ListActionLog
				layerStyle="card"
				changedSecureId={specialty.secure_id}
				type="specialty"
			/>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		const api = setupApiClient(ctx)
		const { data } = await api.get(`/v1/admin/specialties/${id}`)

		return {
			props: {
				specialty: data
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["specialties_edit"],
	}
)

export default SpecialtiesEdit
