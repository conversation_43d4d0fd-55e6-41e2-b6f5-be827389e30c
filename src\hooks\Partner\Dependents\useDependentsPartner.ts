import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { FormatDateForDayMonthYearUsingBars } from "~/utils/Functions/FormatDates"
import { ListUserAdmin } from "~/utils/Types/Admin/User"

type ListDependentAdmin = ListUserAdmin & {
  parent: {
    secure_id: string
    name: string
  }
}

type GetDependentsResponse = {
  total: number
  page: number
  lastPage: number
  perPage: number
  dependents: ListDependentAdmin[]
}

type GetDependentsProps = {
  page: number
  search?: string
  limit?: number
  parent: string
}

export async function getDependentsPartner({ page, search, limit, parent }: GetDependentsProps): Promise<GetDependentsResponse> {
  const response = await api.get('/v1/partner/dependents-partner', {
    params: {
      search,
      page,
      limit,
      parent
    }
  })

  const dependents = response.data.data.map((user: any) => ({
    secure_id: user.secure_id,
    name: user.userInfo.name,
    legal_document_number: user.userInfo.legal_document_number,
    birth_date: user.userInfo.birth_date ? FormatDateForDayMonthYearUsingBars(user.userInfo.birth_date) : ''
  }))

  return {
    total: response.data.meta.total,
    perPage: response.data.meta.per_page,
    page: response.data.meta.current_page,
    lastPage: response.data.meta.last_page,
    dependents
  }
}

export function useDependentsPartner({ page, search, limit, parent }: GetDependentsProps) {
  return useQuery(['DependentsPartner', page, search, limit, parent], () => getDependentsPartner({ page, search, limit, parent }))
}
