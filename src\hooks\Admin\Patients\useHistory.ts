import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { masks } from "~/utils/Functions/Masks"

type GetHistory = {
	secureId: string
	page: number
	search?: string
	limit?: number
	status?: string
	direction?: 'asc' | 'desc'
	field?: string;
}

type GetHistoryApiResponse = {
	data: PatientHistoryData[];
	meta: Meta
}

type PatientHistoryData = {
	type: 'appointment' | 'schedule';
	secureId: string;
	status: string;
	specialtyOrExam: string;
	solicitationDate: string;
	
	appointmentSecureId: string;
	appointmentDate: string;

	partner: {
		secureId: string;
		name: string;
	}
}

type Meta = {
	total: number;
	per_page: number;
	current_page: number;
	last_page: number;
	first_page: number;
	first_page_url: string | null;
	last_page_url: string | null;
	next_page_url: string | null;
	previous_page_url: string | null;
}

type GetPatientHistoryResponse = {
	history: PatientHistoryData[];
	meta: Meta
}

export async function getPatientsAdmin({  secureId, page, search, direction, field, limit, status }: GetHistory): Promise<GetPatientHistoryResponse> {
	const historyResponse = await api.get<GetHistoryApiResponse>(`/v1/admin/patients-history/${secureId}`, {
		params: {
			search,
			page,
			limit,
			direction,
			field: !!field ? field : undefined,
			status: (!status || status === 'all') ? undefined : [status]
		}
	});

	const history = historyResponse.data.data;
	const meta = historyResponse.data.meta;

	return {
		history,
		meta
	}
}

export function useHistory({ secureId, page, search, direction, field, limit, status }: GetHistory) {
	return useQuery([
		'PatientHistory', 
		secureId,
		page, 
		search, 
		limit,
		status,
		direction,
		field
		], 
		() => getPatientsAdmin({ secureId, page, search, direction, field, limit, status })
	)
}
