import {
	VStack,
	<PERSON>,
	<PERSON><PERSON>,
	TabList,
	Tab,
	TabPanels,
	TabPanel,
	useToast,
	Grid,
	GridItem,
	Divider,
	Flex,
	HStack,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { yupResolver } from "@hookform/resolvers/yup"

import { setupApiClient } from "~/services/api"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { ShowUser, UserFormData } from "~/utils/Types/Admin/User"

import { TabDependents } from "~/components/Admin/Patient/TabDependents"
import { AxiosError, AxiosResponse } from "axios"
import { queryClient } from "~/services/queryClient"
import { api } from "~/services/apiClient"
import { useMutation } from "@tanstack/react-query"
import { FormatDateForYearMonthDay } from "~/utils/Functions/FormatDates"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"
import { useEffect } from "react"
import { InputImage } from "~/components/global/Form/ImputImage"
import { Input } from "~/components/global/Form/Input"
import { InputMask } from "~/components/global/Form/InputMask"
import { InputDate } from "~/components/global/Form/InputDate"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog"
import { Option } from "~/utils/Types/Global"
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect"
import { InputSelect } from "~/components/global/Form/InputSelect"

interface PatientsEditProps {
	patient: ShowUser & {
		partners: ShowUser[]
	}
}

type FormData = UserFormData & {
	origin: string
	partnerSecureId: Option

	isActive: 'true' | 'false'
}

const FormSchema = yup.object().shape({
	name: yup.string().required("Nome obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	password: yup
		.string()
		.test('required-min-length', 'Muito curta...', function (value) {
			const isRequired = !!(this.resolve(yup.ref('password')));
			if (!isRequired) {
				return true;
			}
			return value && value.trim().length >= 6 || new yup.ValidationError('Muito curta...', null, this.path);
		}),
	passwordConfirmation: yup
		.string()
		.oneOf([yup.ref("password")], "As senha precisam ser iguais"),
	legal_document_number: yup.string().required("CPF obrigatório"),
	cell: yup.string().required("Celular obrigatório"),
	isActive: yup.mixed<'true' | 'false'>().oneOf(['true', 'false']),
})

interface FormEditPatientAdminProps {
	patient: ShowUser
}

const PatientsEdit: NextPage<PatientsEditProps> = ({ patient }) => {
	const toast = useToast()

	const {
		register,
		formState: { errors },
		formState,
		handleSubmit,
		watch,
		setValue,
		clearErrors,
		control
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			name: patient.userInfo.name,
			email: patient.email,
			legal_document_number: patient.userInfo.legal_document_number,
			cell: `${patient.userInfo.ddd_cell}${patient.userInfo.cell}`,
			zip_code: patient.userInfo.zip_code,
			street: patient.userInfo.street,
			number: patient.userInfo.number,
			complement: patient.userInfo.complement,
			neighborhood: patient.userInfo.neighborhood,
			city: patient.userInfo.city,
			state: patient.userInfo.state,
			birth_date: patient.userInfo.birth_date ? FormatDateForYearMonthDay(patient.userInfo.birth_date) : "",
			avatarSecureId: patient.avatar ? patient.avatar.secure_id : undefined,
			origin: patient.userInfo.origin,
			partnerSecureId: patient.partners[0] ? { label: patient.partners[0].userInfo.name, value: patient.partners[0].secure_id } : undefined,
			isActive: patient.isActive ? 'true' : 'false',
		},
	})

	const handleSearchPartners = async (search: string) => {
		const { data } = await api.get('v1/admin/list/partners', {
			params: {
				search,
			}
		})

		const partners = data.map((user: any) => ({ label: user.userInfo.name, value: user.secure_id }))

		return partners
	}

	const edit = useMutation(
		async (values: FormData) => {
			const newCell = values.cell.replace(/\D/g, '')
			const ddd_cell = newCell.slice(0, 2)
			const cell = newCell.slice(2)

			return await api.put(`/v1/admin/patients/${patient.secure_id}`, {
				...values,
				ddd_cell,
				cell,
				partnerSecureId: values.partnerSecureId ? values.partnerSecureId.value : undefined,
				isActive: values.isActive === 'true' ? true : false,
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["PatientsAdmin"])
				toast({
					title:
						response.data?.message || "Paciente alterado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao alterar paciente.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleEdit: SubmitHandler<FormData> = async (values) => {
		try {
			await edit.mutateAsync(values)
		} catch {}
	}

	const getCep = async (value: string) => {
		if (value && value.length === 8) {
			api.get(`https://brasilapi.com.br/api/cep/v2/${value}`).then(response => {
				const data: any = response.data;
				setValue('street', data.street)
				setValue('city', data.city)
				setValue('neighborhood', data.neighborhood)
				setValue('state', data.state)
			}).catch(() => {
				// setBairro('')
				// setCity('')
			})
		}
	}

	const zipCode = watch('zip_code')
	useEffect(() => {
		if (zipCode) { getCep(zipCode.replace(/[^\d]/g, '')) }
	}, [zipCode])

	useEffect(() => {
		setValue('cell', watch('cell'))
	}, [])

	return (
		<VStack spacing="4" layerStyle="container">
			<Box p="4" width="100%" layerStyle="card">
				<Tabs variant="enclosed" size="lg" isFitted flex="1">
					<TabList>
						<Tab textStyle="headerSM">Informações</Tab>
						<Tab textStyle="headerSM">Dependentes</Tab>
					</TabList>
					<TabPanels
						layerStyle="tabContainer"
					>
						<TabPanel as="form" onSubmit={handleSubmit(handleEdit)}>
							<Grid
								templateColumns={{
									sm: 'repeat(4, 1fr)',
									md: 'repeat(8, 1fr)',
									lg: 'repeat(10, 1fr)',
									xl: 'repeat(12, 1fr)',
									'2xl': 'repeat(12, 1fr)',
								}}
								gap={6}
								w="100%"
							>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 4 }} rowSpan={{ sm: 8, lg: 2 }}>
									<InputImage
										name="avatarSecureId"
										label="Avatar"
										watch={watch}
										setValue={setValue}
										clearErrors={clearErrors}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, '2xl': 8 }}>
									<Input
										placeholder="Nome *"
										label="Nome *"
										error={errors.name}
										{...register("name")}
									/>
								</GridItem>

								<GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, '2xl': 8 }}>
									<Input
										placeholder="E-mail *"
										label="E-mail *"
										type="email"
										error={errors.email}
										{...register("email")}
									/>
								</GridItem>

								<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 4 }}>
									<Input
										label="CPF *"
										placeholder="CPF *"
										error={errors.legal_document_number}
										{...register('legal_document_number')}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 4 }}>
									<InputMask
										label="Celular *"
										placeholder="Celular *"
										mask="(99)99999-9999"
										error={errors.cell}
										{...register('cell')}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 4 }}>
									<InputDate
										label="Data de nascimento *"
										placeholder="Data de nascimento *"
										{...register('birth_date')}
										error={errors.birth_date}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
									<Divider />
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 3, '2xl': 2 }}>
									<Input
										placeholder="Cep"
										label="Cep"
										error={errors.zip_code}
										{...register("zip_code")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 7, xl: 9, '2xl': 8 }} >
									<Input
										placeholder="Endereço"
										label="Endereço"
										error={errors.street}
										{...register("street")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 2, xl: 3, '2xl': 2 }}>
									<Input
										placeholder="Número"
										label="Número"
										error={errors.number}
										{...register("number")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 8, xl: 9, '2xl': 4 }}>
									<Input
										placeholder="Complemento"
										label="Complemento"
										error={errors.complement}
										{...register("complement")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, '2xl': 3 }}>
									<Input
										placeholder="Bairro"
										label="Bairro"
										error={errors.neighborhood}
										{...register("neighborhood")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 3 }}>
									<Input
										placeholder="Cidade"
										label="Cidade"
										error={errors.city}
										{...register("city")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, '2xl': 2 }}>
									<Input
										placeholder="Estado"
										label="Estado"
										error={errors.state}
										{...register("state")}
									/>
								</GridItem>
							</Grid>
							<Grid
								templateColumns={{
									sm: 'repeat(4, 1fr)',
									md: 'repeat(8, 1fr)',
									lg: 'repeat(10, 1fr)',
									xl: 'repeat(12, 1fr)',
									'2xl': 'repeat(12, 1fr)',
								}}
								gap={6}
								w="100%"
								pt={5}
							>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
									<Divider />
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
									<Input
										placeholder="Origem"
										label="Origem"
										error={errors.origin}
										{...register("origin")}
									/>
								</GridItem>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
									<InputAsyncSelect
										isMulti={false}
										name="partnerSecureId"
										label="Parceiro"
										placeholder="Procure um parceiro"
										control={control}
										error={errors.partnerSecureId}
										handleSearch={handleSearchPartners}
									/>
								</GridItem>
							</Grid>
							
							<Grid
								templateColumns={{
									sm: 'repeat(4, 1fr)',
									md: 'repeat(8, 1fr)',
									lg: 'repeat(10, 1fr)',
									xl: 'repeat(12, 1fr)',
									'2xl': 'repeat(12, 1fr)',
								}}
								gap={6}
								w="100%"
								pt={5}
							>
								<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
									<Divider />
								</GridItem>
								
								<GridItem colSpan={{ sm: 4, md: 4, lg: 4, xl: 4, '2xl': 4 }}>
									<InputSelect
										label="Status"
										options={[
											{ label: "Ativo", value: "true" },
											{ label: "Inativo", value: "false" },
										]}
										// isDisabled={isDisabledChangeStatus}
										{...register("isActive")}
									/>
								</GridItem>
							</Grid>

							<Flex justify="flex-end" w="100%" pt={5}>
								<HStack spacing="4" width="20em">
									<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
									<ButtonSubmit isLoading={formState.isSubmitting}>
										Salvar
									</ButtonSubmit>
								</HStack>
							</Flex>
						</TabPanel>
						<TabPanel>
							<TabDependents
								parent={patient.secure_id}
							/>
						</TabPanel>
					</TabPanels>
				</Tabs>
			</Box>
			<ListActionLog
				layerStyle="card"
				changedSecureId={patient.secure_id}
				type="user"
			/>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		const api = setupApiClient(ctx)
		const { data } = await api.get(`/v1/admin/patients/${id}`)

		return {
			props: {
				patient: data
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["patients_edit"],
	}
)

export default PatientsEdit
