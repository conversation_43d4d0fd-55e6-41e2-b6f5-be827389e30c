import { Flex, Image, Text, VStack, useToast } from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { yupResolver } from "@hookform/resolvers/yup"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { WithSSRGuest } from "~/utils/Validator/WithSSRGuest"

import { Input } from "~/components/global/Form/Input"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { useRouter } from "next/router"
import { useMutation } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { AxiosError, AxiosResponse } from "axios"

type FormData = {
	password: string;
	passwordConfirmation: string;
}

type ResetPasswordProps = {
	token: string;
}

const FormSchema = yup.object().shape({
	password: yup.string().min(8, 'Pelo menos 8 caracteres').matches(
		/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/])[A-Za-z\d!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/]{6,}$/,
		'A senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.'
	),
	passwordConfirmation: yup.string().oneOf([
		'',
		yup.ref('password')
	], 'As senhas precisam ser iguais'),
})

const ResetPasswordAdmin: NextPage<ResetPasswordProps> = ({ token }) => {
	const toast = useToast()
	const router = useRouter()

	const { register, handleSubmit, formState, formState: { errors } } = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
	})

	const resetPassword = useMutation(async (data: FormData) => {

		return await api.put('/v1/reset_password', {
			token,
			password: data.password,
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			toast({
				title: response.data?.message || 'Senha alterada com sucesso!',
				position: "top-right",
				status: "success",
				isClosable: true,
			})
			router.push('/login')
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao atualizar senha.',
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	})

	const handleForgot: SubmitHandler<FormData> = async (values) => {
		try {
			await resetPassword.mutateAsync(values)
		} catch {}
	}

	return (
		<Flex
			h="100vh"
			align="center"
			justify="center"

		>
			<VStack
				as="form"
				p="6"
				w="400px"
				spacing="5"
				layerStyle="card"
				onSubmit={handleSubmit(handleForgot)}
			>
				<Image
					src="/hellomed.png"
					alt="logo"
					maxW="200px"
					objectFit="contain"
				/>
				<Text fontSize="xl" fontWeight="medium">Redefinir ou criar senha</Text>
				<VStack
					w="100%"
					spacing="4"
				>
					<Input
						type="password"
						label="Nova Senha"
						placeholder="Digite uma nova senha"
						error={errors.password}
						{...register('password')}
					/>
					<Input
						type="password"
						label="Confirme sua Nova Senha"
						placeholder="Digite novamente sua nova senha"
						error={errors.passwordConfirmation}
						{...register('passwordConfirmation')}
					/>
					<VStack w="100%">
						<ButtonSubmit
							isLoading={formState.isSubmitting}
							loadingText="Salvando..."
						>
							Alterar
						</ButtonSubmit>
					</VStack>
				</VStack>
			</VStack>
		</Flex>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRGuest(async (ctx) => {
	const { token } = ctx.query
	return {
		props: {
			token
		}
	}
})

export default ResetPasswordAdmin
