import {
  Box,
  Divider,
  Flex,
  Grid,
  Grid<PERSON>tem,
  HStack,
  <PERSON>b,
  TabList,
  TabPanel,
  TabPanels,
  <PERSON><PERSON>,
} from "@chakra-ui/react";
import { Input } from "~/components/global/Form/Input";
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit";
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit";
import { InputImage } from "~/components/global/Form/ImputImage";
import { InputCreatableSelect } from "~/components/global/Form/InputCreatableSelect";
import { InputDate } from "~/components/global/Form/InputDate";
import { InputMask } from "~/components/global/Form/InputMask";
import { InputPassword } from "~/components/global/Form/InputPassword";
import { InputRadio } from "~/components/global/Form/InputRadio";
import { InputSelectMult } from "~/components/global/Form/InputSelectMult";
import { TabDoc<PERSON> } from "../TabDoctors";
import {
  Control,
  FieldErrors,
  FormState,
  SubmitHandler,
  UseFormClearErrors,
  UseFormHandleSubmit,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { UserFormData } from "~/utils/Types/Admin/User";
import { Option, typesDoctors, typesOfCares, typesStatus } from "~/utils/Types/Global";
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect";
import { InputNumberMask } from "~/components/global/Form/InputNumberMask";
import { ShowDoctor } from "~/pages/admin/accrediteds/[id]";

type FormData = UserFormData & {
  adviceRegister: string;
  type: string;
  typeOfCare: string;
  status: string;
  groupSecureId: Option
  paymentMethods: Option[];
  specialtiesSecureIds: Option[];
  examsSecureIds: Option[]
  queryValue: number;
  accreditedValue: number;
};

interface AccreditedTabsPanelProps {
  clinicId: string;
  accredited: ShowDoctor;
  watch: UseFormWatch<FormData>;
  formState: FormState<FormData>;
  register: UseFormRegister<FormData>;
  setValue: UseFormSetValue<FormData>;
  clearErrors: UseFormClearErrors<FormData>;
  errors: FieldErrors<FormData>;
  control: Control<FormData, any>;
  specialtiesOptions: Option[];
  handleSubmit: UseFormHandleSubmit<FormData>;
  handleEdit: SubmitHandler<FormData>;
  handleSearchGroups: (search: string) => Promise<any>
}

export function AccreditedTabsPanel({
  clinicId,
  accredited,
  register,
  setValue,
  formState,
  clearErrors,
  watch,
  errors,
  control,
  specialtiesOptions,
  handleSubmit,
  handleEdit,
  handleSearchGroups
}: AccreditedTabsPanelProps) {

  return (
    <Box p="4" width="100%" layerStyle="card">
      <Tabs variant="enclosed" size="lg" isFitted flex="1">
        <TabList>
          <Tab textStyle="headerSM">Informações</Tab>
          <Tab textStyle="headerSM">Médicos</Tab>
        </TabList>
        <TabPanels layerStyle="tabContainer">
          <TabPanel as="form" onSubmit={handleSubmit(handleEdit)}>
            <Grid
              templateColumns={{
                sm: "repeat(4, 1fr)",
                md: "repeat(8, 1fr)",
                lg: "repeat(10, 1fr)",
                xl: "repeat(12, 1fr)",
                "2xl": "repeat(12, 1fr)",
              }}
              gap={6}
              w="100%"
              alignItems="center"
            >
              <GridItem
                colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}
                rowSpan={{ sm: 8, lg: 2 }}
              >
                <InputImage
                  name="avatarSecureId"
                  label="Avatar"
                  watch={watch}
                  setValue={setValue}
                  clearErrors={clearErrors}
                  maxHeight="200px"
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, "2xl": 8 }}>
                <Input
                  placeholder="Nome *"
                  label="Nome *"
                  error={errors.name}
                  {...register("name")}
                />
              </GridItem>

              <GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, "2xl": 8 }}>
                <Input
                  placeholder="E-mail *"
                  label="E-mail *"
                  type="email"
                  error={errors.email}
                  {...register("email")}
                />
              </GridItem>

              <GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}>
                <Input
                  label="CPF *"
                  placeholder="CPF *"
                  error={errors.legal_document_number}
                  {...register("legal_document_number")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 4 }}>
                <InputMask
                  label="Celular *"
                  placeholder="Celular *"
                  mask="(99)99999-9999"
                  error={errors.cell}
                  {...register("cell", { onChange: () => { } })}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 4 }}>
                <InputDate
                  label="Data de Nascimento/Fundação *"
                  placeholder="Data de Nascimento/Fundação *"
                  {...register("birth_date")}
                  error={errors.birth_date}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <Divider />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 3, "2xl": 2 }}>
                <Input
                  placeholder="Cep"
                  label="Cep"
                  error={errors.zip_code}
                  {...register("zip_code")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 7, xl: 9, "2xl": 8 }}>
                <Input
                  placeholder="Endereço"
                  label="Endereço"
                  error={errors.street}
                  {...register("street")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 2, xl: 3, "2xl": 2 }}>
                <Input
                  placeholder="Número"
                  label="Número"
                  error={errors.number}
                  {...register("number")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 8, xl: 9, "2xl": 4 }}>
                <Input
                  placeholder="Complemento"
                  label="Complemento"
                  error={errors.complement}
                  {...register("complement")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 3 }}>
                <Input
                  placeholder="Bairro"
                  label="Bairro"
                  error={errors.neighborhood}
                  {...register("neighborhood")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
                <Input
                  placeholder="Cidade"
                  label="Cidade"
                  error={errors.city}
                  {...register("city")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 2 }}>
                <Input
                  placeholder="Estado"
                  label="Estado"
                  error={errors.state}
                  {...register("state")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <Divider />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}>
                <Input
                  placeholder="Registro *"
                  label="Registro *"
                  error={errors.adviceRegister}
                  {...register("adviceRegister")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
                <InputRadio
                  label="Tipo de atendimento"
                  name="typeOfCare"
                  control={control}
                  options={typesOfCares}
                  error={errors.typeOfCare}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
                <InputRadio
                  label="Tipo"
                  name="type"
                  control={control}
                  options={typesDoctors}
                  error={errors.typeOfCare}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 1 }}>
                <InputRadio
                  label="Status"
                  name="status"
                  control={control}
                  options={typesStatus}
                  error={errors.status}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 6.666666, xl: 8, "2xl": 8 }}>
                <InputCreatableSelect
                  name="paymentMethods"
                  label="Métodos de pagamento *"
                  placeholder="Digite um método de pagamento *"
                  noOptionsMessage={() => ""}
                  error={errors.paymentMethods as any}
                  control={control}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3.333333, xl: 4, "2xl": 4 }}>
                <InputNumberMask
                  setValue={setValue}
                  placeholder="R$ 0,00"
                  prefix="R$ "
                  error={errors.queryValue}
                  value={accredited.userInfo.query_value
                    ? accredited.userInfo.query_value / 100
                    : ""}
                  label="Valor Hellomed"
                  name="queryValue"
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3.333333, xl: 12, "2xl": 12 }}>
                <InputNumberMask
                  setValue={setValue}
                  placeholder="R$ 0,00"
                  prefix="R$ "
                  error={errors.accreditedValue}
                  value={accredited.userInfo.accredited_value
                    ? accredited.userInfo.accredited_value / 100
                    : ""}
                  label="Valor Credenciado"
                  name="accreditedValue"
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <InputSelectMult
                  name="specialtiesSecureIds"
                  label="Especialidades"
                  placeholder="Especialidades"
                  options={specialtiesOptions}
                  control={control}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <InputAsyncSelect
                  isMulti={false}
                  isClearable
                  defaultOptions
                  control={control}
                  name="groupSecureId"
                  error={errors.groupSecureId}
                  label="Grupo"
                  placeholder="Procure um grupo"
                  handleSearch={handleSearchGroups}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <Divider />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
                <InputPassword
                  label="Senha"
                  placeholder="Senha"
                  error={errors.password}
                  {...register("password")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
                <InputPassword
                  label="Confirmação de Senha"
                  placeholder="Repetir a Senha"
                  error={errors.passwordConfirmation}
                  {...register("passwordConfirmation")}
                />
              </GridItem>
            </Grid>

            <Flex justify="flex-end" w="100%" mt={6}>
              <HStack spacing="4" width="20em">
                <ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
                <ButtonSubmit
                  type="submit"
                  isLoading={formState.isSubmitting}
                >
                  Salvar
                </ButtonSubmit>
              </HStack>
            </Flex>
          </TabPanel>
          <TabPanel>
            <TabDoctors clinicId={clinicId} />
          </TabPanel>
        </TabPanels>
      </Tabs>
    </Box>
  );
};
