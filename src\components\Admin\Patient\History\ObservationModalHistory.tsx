import {
	Button,
	Checkbox,
	Grid,
	GridItem,
	HStack,
	Icon,
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	<PERSON>dal<PERSON>ooter,
	ModalHeader,
	ModalOverlay,
	Spinner,
	Stack,
	Table,
	TableContainer,
	Tbody,
	Td,
	Th,
	Thead,
	Tr,
	useDisclosure,
	useToast,
	VStack,
	Text,
	Box
} from "@chakra-ui/react"
import { FC, useCallback } from "react"

import { useAppointmentObservations } from "~/hooks/Admin/Appointment/useAppointmentObservations"
import { FaRegTrashAlt } from "react-icons/fa"
import { RiPencilLine } from "react-icons/ri"
import { useForm } from "react-hook-form"
import { yupResolver } from "@hookform/resolvers/yup"
import { createObservationSchema, CreateObservationSchema } from "./schema/createObservationSchema"
import { Input } from "~/components/global/Form/Input"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { AxiosError } from "axios"
import { editObservationSchema, EditObservationSchema } from "./schema/editObservationSchema"
import { deleteObservationSchema, DeleteObservationSchema } from "./schema/deleteObservationSchema"

interface ObservationModalProps {
	isOpen: boolean
	onClose: () => void
	onToggle: () => void

	scheduleSecureId: string
	type: 'appointment' | 'schedule'
}

type EditObservationModalProps = {
	secureId: string;
	observation: string;
};

export const ObservationModalHistory: FC<ObservationModalProps> = ({ 
	onClose, 
	isOpen, 
	scheduleSecureId,
	type
}) => {
	const { 
		data: observationData,
		isLoading: isObservationHookLoading,
		isFetching: isObservationHookFetching,
	} = useAppointmentObservations(scheduleSecureId);

	const isObservationLoading = isObservationHookLoading || isObservationHookFetching;

	const toast = useToast();

	const {
		isOpen: isCreateObservationModalOpen,
		onOpen: onOpenCreateObservationModal,
		onClose: onCloseCreateObservationModal
	} = useDisclosure();

	const { 
		isOpen: isOpenDeleteObservationModal,
		onOpen: onOpenDeleteObservationModal,
		onClose: onCloseDeleteObservationModal,
	} = useDisclosure();

	const { 
		isOpen: isOpenEditObservationModal,
		onOpen: onOpenEditObservationModal,
		onClose: onCloseEditObservationModal, 
	} = useDisclosure();

	const handleCloseCreateObservation = useCallback(() => {
		onCloseCreateObservationModal()
	}, []);

	const createObservationForm = useForm<CreateObservationSchema>(
		{ resolver: yupResolver(createObservationSchema),
			defaultValues: {
				observation: ''
			}
		}
	);
	const editObservationForm = useForm<EditObservationSchema>(
		{ resolver: yupResolver(editObservationSchema),
			defaultValues: {
				observation: '',
				observationSecureId: '',
			}
		}
	);
	const deleteObservationForm = useForm<DeleteObservationSchema>(
		{ resolver: yupResolver(deleteObservationSchema),
			defaultValues: {
				observation: '',
				observationSecureId: '',
			}
		}
	);

	const handleSubmitNewObservation = useCallback(async (data: CreateObservationSchema) => {
		try {
			const createObservationObject = {
				observation: data.observation,
				scheduleSecureId: scheduleSecureId,
				from: type
			};
	
			await api.post('/v1/admin/observations',
				createObservationObject,
			);

			await queryClient.invalidateQueries({
				queryKey: ["AppointmentObservations"]
			});

			await queryClient.invalidateQueries({
				queryKey: ["schedules"]
			});

			toast({
				title: "Observação adicionado com sucesso.",
				position: "top-right",
				status: "success",
				isClosable: true,
			});

			createObservationForm.reset();
			onCloseCreateObservationModal();
		} catch (error) {
			if (error instanceof AxiosError) {
				const message = error?.request?.data?.message;

				toast({
					title:
						message || "Erro ao adicionar nova observação.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			}
		}
	}, [observationData]);

	const handleOpenDeleteObservationModal = (data: DeleteObservationSchema) => {
		onOpenDeleteObservationModal();


		deleteObservationForm.setValue("observation", data.observation);
		deleteObservationForm.setValue("observationSecureId", data.observationSecureId);
	}

	const handleDeleteObservation = useCallback(async (data: DeleteObservationSchema) => {
		try {
			await api.delete(`/v1/admin/observations/${data.observationSecureId}/${type}`);

			await queryClient.invalidateQueries({
				queryKey: ["AppointmentObservations"]
			});

			await queryClient.invalidateQueries({
				queryKey: ["schedules"]
			});

			toast({
				title: "Observação excluída com sucesso.",
				position: "top-right",
				status: "success",
				isClosable: true,
			});

			onCloseDeleteObservationModal();
		} catch (error) {
			if (error instanceof AxiosError) {
				const message = error?.request?.data?.message;

				toast({
					title:
						message || "Erro ao excluir observação.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			}
		}
	}, []);

	const handleEditObservation = useCallback(async (data: EditObservationSchema) => {
		try {
			const editObservationObject = {
				observation: data.observation,
				from: type
			};

			await api.put(`/v1/admin/observations/${data.observationSecureId}`, 
				editObservationObject
			);

			await queryClient.invalidateQueries({
				queryKey: ["AppointmentObservations"]
			});

			await queryClient.invalidateQueries({
				queryKey: ["schedules"]
			});

			toast({
				title: "Observação editada com sucesso.",
				position: "top-right",
				status: "success",
				isClosable: true,
			});

			onCloseEditObservationModal();
		} catch (error) {
			if (error instanceof AxiosError) {
				const message = error?.request?.data?.message;

				toast({
					title:
						message || "Erro ao editar observação.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			}
		}
	}, []);

	const handleOpenEditObservationModal = (data: EditObservationModalProps) => {
		onOpenEditObservationModal();


		editObservationForm.setValue("observation", data.observation);
		editObservationForm.setValue("observationSecureId", data.secureId);
	}

	const handleCloseModal = () => {
		onClose()
	}

	return (
		<>
			<Modal size="2xl" isOpen={isOpen} onClose={handleCloseModal}>
				<ModalOverlay />
				<ModalContent
				>
					<ModalHeader>Observações</ModalHeader>

					<ModalCloseButton />

					<ModalBody>
						<VStack
							width="100%"
							justify="center"
							align="flex-start"
						>
							<Stack flexDirection="row" width="100%" justify="space-between" mb={4}>
								<Button colorScheme="blue" onClick={onOpenCreateObservationModal}>
									Adicionar Observação
								</Button>

								{/* <Button colorScheme="red" onClick={() => {}}>
									Excluir seleção
								</Button> */}
							</Stack>

							<Stack w="100%" background="white" rounded="md" shadow="md" padding={4}>
								<TableContainer>
									<Table whiteSpace="pre-wrap" variant="simple">
										<Thead>
											<Tr>
												{/* <Th paddingY="4" paddingX="2">
													<Checkbox
														isChecked={false}
														// isChecked={allObservationChecked}
														isIndeterminate={false}
														// isIndeterminate={isObservationIndeterminate}
														size="lg"
														// onChange={(e) => {
														// 	setCheckedObservations(
														// 		observations.map((observation) => e.target.checked)
														// 	);
														// }}
													/>
												</Th> */}

												<Th paddingY="4" paddingX="2">Observação</Th>

												<Th paddingY="4" paddingX="2" width='15%' textAlign='center'>Ações</Th>
											</Tr>
										</Thead>
										
										{isObservationLoading ? (
											<Spinner />
										) : (
											<Tbody>
												{observationData && observationData.observations.length > 0 && (
													observationData.observations.map((item, index) => (
														<Tr key={`${item.secureId} - ${index}`}>
															{/* <Td paddingY="4" paddingX="2">
																<Checkbox
																	isChecked={false}
																	// isChecked={checkedObservations[index]}
																	onChange={(e) => {
																		// setCheckedObservations(
																		// 	observations.map((observation, newIndex) =>
																		// 		newIndex === index
																		// 			? e.target.checked
																		// 			: checkedObservations[newIndex]
																		// 	)
																		// );
																	}}
																/>
															</Td> */}
															<Td paddingY="4" paddingX="2">
																{item.observation ? item.observation : "Sem observação"}
															</Td>

															<Td paddingY="4" paddingX="2" mx='auto'>
																<HStack gap={2} justifyContent='center'>
																	<Button
																		size="sm"
																		fontSize="sm"
																		colorScheme="telegram"
																		title="Editar"
																		onClick={() =>
																			handleOpenEditObservationModal({ observation: item.observation, secureId: item.secureId})
																		}
																	>
																		<Icon as={RiPencilLine } fontSize="20" />
																	</Button>
																	
																	<Button
																		size="sm"
																		fontSize="sm"
																		colorScheme="red"
																		title="Excluir"
																		onClick={() =>
																			handleOpenDeleteObservationModal(
																				{ 
																					observation: item.observation, 
																					observationSecureId: item.secureId
																				}
																			)
																		}
																	>
																		<Icon as={FaRegTrashAlt} fontSize="20" />
																	</Button>
																</HStack>
															</Td>
														</Tr>
													)
												))}
											</Tbody>
										)}

									</Table>
								</TableContainer>
							</Stack>
							
						</VStack>
					</ModalBody>
				</ModalContent>
			</Modal>

			{/* Create Observation Modal */}
			<Modal isOpen={isCreateObservationModalOpen} onClose={handleCloseCreateObservation}>
				<ModalOverlay />
				<ModalContent>
					<ModalHeader>Adicionar observação</ModalHeader>

					<ModalBody>
						<Stack backgroundColor="white">
							<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
								<GridItem colSpan={12}>
									<Input
										placeholder="Nova observação"
										error={createObservationForm?.formState?.errors?.observation}
										label="Observação"
										{...createObservationForm.register("observation")}
										isDisabled={createObservationForm.formState.isSubmitting}
									/>
								</GridItem>
							</Grid>
						</Stack>
					</ModalBody>

					<ModalFooter>
						<Button variant="ghost" onClick={handleCloseCreateObservation}>
							Cancelar
						</Button>

						<Button
							colorScheme="blue"
							mr={3}
							onClick={
								createObservationForm.handleSubmit(handleSubmitNewObservation)
							}
							isLoading={createObservationForm.formState.isSubmitting}
						>
							Salvar
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>

			{/* Delete Observation Modal */}
			<Modal isOpen={isOpenDeleteObservationModal} onClose={onCloseDeleteObservationModal}>
				<ModalOverlay />
				<ModalContent>
					<ModalHeader>Apagar observações</ModalHeader>

					<ModalBody>
						<Stack backgroundColor="white">
							<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
								<GridItem colSpan={12}>
									<Text>Tem certeza que deseja excluir a observação?</Text>
									<Text mt={2} p={2} borderRadius={6} backgroundColor='#D8D8D8'>{deleteObservationForm.getValues('observation')}</Text>
								</GridItem>
							</Grid>
						</Stack>
					</ModalBody>

					<ModalFooter>
						<Button variant="ghost" onClick={onCloseDeleteObservationModal}>
							Cancelar
						</Button>
						<Button
							colorScheme="red"
							mr={3}
							onClick={deleteObservationForm.handleSubmit(handleDeleteObservation)}
							isLoading={deleteObservationForm.formState.isSubmitting}
						>
							Excluir
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>

			{/* Edit Observation Modal */}
			<Modal isOpen={isOpenEditObservationModal} onClose={onOpenEditObservationModal}>
				<ModalOverlay />
				<ModalContent>
					<ModalHeader>Editar observações</ModalHeader>

					<ModalBody>
						<Stack backgroundColor="white">
							<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
								<GridItem colSpan={12}>
									<Input
										placeholder="Editar observação"
										error={editObservationForm?.formState?.errors?.observation}
										label="Edição de observação"
										{...editObservationForm.register("observation")}
										isDisabled={editObservationForm.formState.isSubmitting}
									/>
								</GridItem>
							</Grid>
						</Stack>
					</ModalBody>

					<ModalFooter>
						<Button variant="ghost" onClick={onCloseEditObservationModal}>
							Cancelar
						</Button>
						<Button
							colorScheme="blue"
							mr={3}
							onClick={editObservationForm.handleSubmit(handleEditObservation)}
							isLoading={editObservationForm.formState.isSubmitting}
						>
							Editar
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>
		</>
	)
}
