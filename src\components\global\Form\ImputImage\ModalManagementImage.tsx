import {
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	Tab,
	TabList,
	TabPanel,
	TabPanels,
	Tabs
} from "@chakra-ui/react"
import { FC } from "react"
import { UseFormSetValue, UseFormWatch } from "react-hook-form"
import { TabListSelectImage } from "./TabListSelectImage"
import { TabUploadImage } from "./TabUploadImage"

interface ModalManagementImageProps {
	isOpen: boolean
	onClose: () => void
	name: string
	setValue: UseFormSetValue<any>
	watch: UseFormWatch<any>
}

export const ModalManagementImage: FC<ModalManagementImageProps> = ({ isOpen, onClose, setValue, watch, name }) => {
	return (
		<Modal isOpen={isOpen} onClose={onClose} size="xl">
			<ModalOverlay />
			<ModalContent bg="white">
				<ModalHeader>Gerenciamento de imagens</ModalHeader>
				<ModalCloseButton />
				<ModalBody>
					<Tabs isFitted>
						<TabList>
							<Tab>Selecionar imagens</Tab>
							<Tab>Fazer upload de imagem</Tab>
						</TabList>

						<TabPanels>
							<TabPanel>
								<TabListSelectImage
									watch={watch}
									name={name}
									setValue={setValue}
									onClose={onClose}
								/>
							</TabPanel>
							<TabPanel>
								<TabUploadImage
									name={name}
									setValue={setValue}
									onClose={onClose}
								/>
							</TabPanel>
						</TabPanels>
					</Tabs>
				</ModalBody>
			</ModalContent>
		</Modal>
	)
}
