export interface Clinic {
  secure_id: string
  email: string
  remember_me_token: any
  type: string
  created_at: string
  updated_at: string
  doctors: Doctor[]
}

export interface Doctor {
  secure_id: string
  email: string
  remember_me_token: any
  type: string
  created_at: string
  updated_at: string
  userInfo: UserInfo
}

export interface UserInfo {
  name: string;
  legal_document_number: string;
  ddd_cell: number;
  cell: number;
  birth_date: string;
}
