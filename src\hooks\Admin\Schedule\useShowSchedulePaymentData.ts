import { useQuery } from "@tanstack/react-query";
import { api } from "~/services/apiClient";
import { ListScheduleAdmin } from "~/utils/Types/Admin/Schedule";

interface PaymentInfo {
  payment_methods: string;
  query_value: number;
  query_value_subsidy: number;
  query_value_patient: number;
}

type GetScheduleResponse = {
  paymentInfo: PaymentInfo;
};

type GetScheduleProps = {
  partnerId: string;
};

export async function getSchedulePaymentData({
  partnerId,
}: GetScheduleProps): Promise<GetScheduleResponse | null> {
  if (partnerId) {
    const response = await api.get(`/v1/admin/schedules-data/${partnerId}`);
    return {
      paymentInfo: response.data.userInfo,
    };
  }

  return null;
}

export function useShowSchedulePaymentData({ partnerId }: GetScheduleProps) {
  return useQuery(["showPaymentData", partnerId], () =>
    getSchedulePaymentData({ partnerId })
  );
}
