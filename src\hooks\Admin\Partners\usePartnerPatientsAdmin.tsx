import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { Doctor } from "~/utils/Types/Admin/Clinic"

type GetPartnersResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	patients: Doctor[]
}

type GetPartnersProps = {
	page: number
	search?: string
	limit?: number
	partnerId?: string

  status: 'all' | 'active' | 'inactive';
}

export async function getPartnerPatientsAdmin({
	page,
	search,
	limit,
	partnerId,
	status
}: GetPartnersProps): Promise<GetPartnersResponse> {
	const response = await api.get(`/v1/admin/partners/${partnerId}/patients`, {
		params: {
			search,
			page,
			limit,
			status
		},
	})

	const patients = response.data.data

	return {
		total: response.data?.meta.total,
		perPage: response.data?.meta.per_page,
		page: response.data?.meta.current_page,
		lastPage: response.data?.meta.last_page,
		patients,
	}
}

export function usePartnerPatientsAdmin({
	page,
	search,
	limit,
	partnerId,
	status
}: GetPartnersProps) {
	return useQuery(["PartnerPatientsAdmin", partnerId, page, search, limit, status], () =>
		getPartnerPatientsAdmin({ page, search, limit, partnerId, status })
	)
}
