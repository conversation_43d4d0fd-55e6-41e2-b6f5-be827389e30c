import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  ModalClose<PERSON>utton,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalOverlay,
  VStack,
  useToast,
} from "@chakra-ui/react";
import { FC, useEffect, useMemo } from "react";

import { parse } from "date-fns";

import * as yup from "yup";
import { AxiosError, AxiosResponse } from "axios";
import { useMutation } from "@tanstack/react-query";
import { yupResolver } from "@hookform/resolvers/yup";
import { SubmitHandler, useForm } from "react-hook-form";

import { read, utils } from "xlsx";

import { api } from "~/services/apiClient";
import { queryClient } from "~/services/queryClient";
import { typesDoctors, typesOfCares } from "~/utils/Types/Global";

import { InputDocFile } from "~/components/global/Form/InputDocFile";
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit";
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel";
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit";

// Tipos para validação
interface ValidationRule {
  columnName: string;
  required: boolean;
  customMessage?: string;
  validator?: (value: any, rowIndex: number) => string | null;
}

interface ValidationError {
  message: string;
  row: number;
  column: string;
}

const validationRules: ValidationRule[] = [
  {
    columnName: "Nome",
    required: true,
    customMessage: "Coluna Nome contém linha em branco",
  },
  {
    columnName: "E-mail",
    required: false,
  },
  {
    columnName: "CNPJ/CPF",
    required: false,
    customMessage: "Coluna CNPJ/CPF contém linha em branco",
  },
  {
    columnName: "Celular",
    required: false,
  },
  {
    columnName: "Data de Nascimento/Fundação (##/##/####)",
    required: false,
    customMessage: "Coluna Data de Nascimento/Fundação contém linha em branco",
    validator: (value: string, rowIndex: number) => {
      if (!value) return null;
      const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
      if (!dateRegex.test(value)) {
        return `Coluna Data de Nascimento/Fundação contém formato inválido na linha ${
          rowIndex + 2
        }. Use o formato DD/MM/AAAA`;
      }
      return null;
    },
  },
  {
    columnName: "Registro",
    required: false,
    customMessage: "Coluna Registro contém linha em branco",
  },
  {
    columnName: "CEP",
    required: false,
    customMessage: "Coluna CEP contém linha em branco",
  },
  {
    columnName: "Endereço",
    required: false,
    customMessage: "Coluna Endereço contém linha em branco",
  },
  {
    columnName: "Número",
    required: false,
    customMessage: "Coluna Número contém linha em branco",
  },
  {
    columnName: "Bairro",
    required: false,
    customMessage: "Coluna Bairro contém linha em branco",
  },
  {
    columnName: "Cidade",
    required: true,
    customMessage: "Coluna Cidade contém linha em branco",
  },
  {
    columnName: "Estado",
    required: true,
    customMessage: "Coluna Estado contém linha em branco",
  },
  {
    columnName: "Métodos de Pagamento",
    required: false,
    customMessage: "Coluna Métodos de Pagamento contém linha em branco",
  },
  {
    columnName: "Tipo de Atendimento (Presencial, Vídeo, Ambos)",
    required: false,
  },
  {
    columnName: "Tipo (Médico, Clínica, Hospital, Laboratório)",
    required: true,
    customMessage: "Coluna Tipo contém linha em branco",
    validator: (value: string, rowIndex: number) => {
      if (!value) return null;
      const validTypes = typesDoctors.map((type) => type.label);
      if (!validTypes.includes(value)) {
        return `Coluna Tipo contém valor inválido na linha ${
          rowIndex + 2
        }. Valores aceitos: ${validTypes.join(", ")}`;
      }
      return null;
    },
  },
  {
    columnName: "Valor",
    required: false,
    validator: (value: string, rowIndex: number) => {
      if (!value) return null;
      const numericValue = value.replace(",", ".");
      if (isNaN(Number(numericValue))) {
        return `Coluna Valor contém formato inválido na linha ${
          rowIndex + 2
        }. Use formato numérico (ex: 150,00)`;
      }
      return null;
    },
  },
  {
    columnName: "Complemento",
    required: false,
  },
  {
    columnName: "Especialidades (Separadas por vírgula)",
    required: false,
  },
  {
    columnName: "Exames (Separados por vírgula)",
    required: false,
  },
  {
    columnName: "Senha (Não obrigatório)",
    required: false,
  },
];

const validateExcelData = (data: any[]): ValidationError[] => {
  const errors: ValidationError[] = [];

  data.forEach((row, rowIndex) => {
    validationRules.forEach((rule) => {
      const value = row[rule.columnName];

      if (rule.required && (!value || value.toString().trim() === "")) {
        errors.push({
          message: `${rule.customMessage} na linha ${rowIndex + 2}`,
          row: rowIndex + 2,
          column: rule.columnName,
        });
      }

      if (rule.validator && value) {
        const customError = rule.validator(value, rowIndex);
        if (customError) {
          errors.push({
            message: customError,
            row: rowIndex + 2,
            column: rule.columnName,
          });
        }
      }
    });
  });

  return errors;
};

const FormSchema = yup.object().shape({
  file: yup.mixed().required("Você deve importar o arquivo Excel"),
  // users: yup.array().of(yup.object({
  // 	name: yup.string().required(),
  // 	email: yup.string().required(),
  // 	legalDocumentNumber: yup.string().required(),
  // 	dddCell: yup.number().required(),
  // 	cell: yup.number().required(),
  // 	birthDate: yup.string().required(),
  // 	adviceRegister: yup.string().required(),
  // 	paymentMethods: yup.string().required(),
  // 	typeOfCare: yup.mixed<'in_person' | 'video_call' | 'both'>().oneOf(['in_person', 'video_call', 'both']).required(),
  // 	type: yup.mixed<'doctor' | 'clinic' | 'hospital' | 'lab'>().oneOf(['doctor', 'clinic', 'hospital', 'lab']).required()
  // }).required()).optional()
});

type FormData = {
  file: File;
  users?: {
    name: string;
    email: string;
    legalDocumentNumber: string;
    dddCell: number;
    cell: number;
    birthDate: string;
    password?: string;
    adviceRegister: string;
    paymentMethods: string;
    typeOfCare: "in_person" | "video_call" | "both";
    type: "doctor" | "clinic" | "hospital" | "lab";
  }[];
};

interface ModalImportAccreditedProps {
  isOpen: boolean;
  closeModal: () => void;
}

export const ModalImportAccredited: FC<ModalImportAccreditedProps> = ({
  closeModal,
  isOpen,
}) => {
  const toast = useToast();

  const { handleSubmit, register, formState, watch, control, reset, setValue } =
    useForm<FormData>({
      resolver: yupResolver<any>(FormSchema),
      defaultValues: {},
    });
  const { errors } = formState;

  const file = watch("file");
  const users = watch("users");

  const readExcel = (fileRead: File) => {
    const promise = new Promise((resolve, reject) => {
      const fileReader = new FileReader();
      fileReader.readAsArrayBuffer(fileRead);

      fileReader.onload = (e) => {
        const bufferArray = e.target?.result;

        const wb = read(bufferArray, {
          type: "file",
          cellDates: true,
          cellText: false,
        });

        const wsname = wb.SheetNames[0];

        const ws = wb.Sheets[wsname];

        const data = utils.sheet_to_json(ws, {
          raw: false,
          dateNF: "DD/MM/YYYY",
        });

        resolve(data);
      };
    });

    promise
      .then((d: any) => {
        // Validar os dados antes de processar
        const validationErrors = validateExcelData(d);

        if (validationErrors.length > 0) {
          // Mostrar todos os erros de validação
          const errorMessages = validationErrors
            .map((error) => error.message)
            .join("\n\n");
          toast({
            title: "Erros encontrados no arquivo:",
            description: errorMessages,
            position: "top-right",
            status: "error",
            isClosable: true,
            duration: 10000, // 10 segundos para dar tempo de ler
          });
          setValue("users", []);
          return;
        }

        setValue(
          "users",
          d.map((user: any) => {
            if (!user["Celular"]) {
              user["Celular"] = "00000000000";
            }
            const newCell = user["Celular"]?.replace(/\D/g, "");
            const dddCell = newCell.slice(0, 2);
            const cell = newCell.slice(2);

            return {
              name: user["Nome"],
              email: user["E-mail"],
              legalDocumentNumber: user["CNPJ/CPF"],
              dddCell,
              cell,
              birthDate: parse(
                user["Data de Nascimento/Fundação (##/##/####)"],
                "dd/MM/yyyy",
                new Date()
              ),
              adviceRegister: user["Registro"],
              paymentMethods: user["Métodos de Pagamento"],
              typeOfCare: typesOfCares.find(
                (typeOfCare) =>
                  typeOfCare.label ===
                  user["Tipo de Atendimento (Presencial, Vídeo, Ambos)"]
              )?.value,
              type: typesDoctors.find(
                (typeDoctor) =>
                  typeDoctor.label ===
                  user["Tipo (Médico, Clínica, Hospital, Laboratório)"]
              )?.value,
              password: user["Senha (Não obrigatório)"],
              zipCode: user["CEP"],
              street: user["Endereço"],
              number: user["Número"],
              complement: user["Complemento"],
              neighborhood: user["Bairro"],
              city: user["Cidade"],
              state: user["Estado"],
              queryValue: user["Valor"]
                ? Number(user["Valor"].replace(",", "."))
                : 0,
              specialtiesNames: user["Especialidades (Separadas por vírgula)"],
              examsNames: user["Exames (Separados por vírgula)"],
            };
          })
        );
      })
      .catch((e) => {
        console.log(e);
        setValue("users", []);
      });
  };

  const dataExportExcel = useMemo(() => {
    return new Array(1).fill(null).map((_, index) => ({
      Nome: `Usuário`,
      "E-mail": `usuario${index + 1}@exemplo.com.br`,
      "CNPJ/CPF": `000.000.000-00`,
      Celular: "(00)00000-0000",
      "Data de Nascimento/Fundação (##/##/####)": "##/##/####",
      Registro: "123",
      CEP: "321",
      Endereço: "Avenida",
      Número: "123",
      Complemento: "",
      Bairro: "Bairro",
      Cidade: "Cidade",
      Estado: "Estado",
      "Métodos de Pagamento": "método1,método2",
      "Tipo de Atendimento (Presencial, Vídeo, Ambos)": "Presencial",
      "Tipo (Médico, Clínica, Hospital, Laboratório)": "Médico",
      Valor: "150,00",
      "Especialidades (Separadas por vírgula)":
        "Psicologia, Fonoaudiologia, Fisioterapia, Ultrassonografia",
      "Exames (Separados por vírgula)":
        "Ultrassonografia, Ressonancia Magnetica",
      "Senha (Não obrigatório)": "Não obrigatório",
    }));
  }, []);

  const add = useMutation(
    async (values: FormData) => {
      return await api.post(`/v1/admin/import-accrediteds`, {
        users: values.users,
      });
    },
    {
      onSuccess: (response: AxiosResponse) => {
        queryClient.invalidateQueries(["AccreditedsAdmin"]);
        handleCloseModal();
        toast({
          title:
            response.data?.message || `Credenciados importados com sucesso!`,
          position: "top-right",
          status: "success",
          isClosable: true,
        });
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title:
            error?.response?.data?.message ||
            error?.response?.data?.errors[0].message ||
            "Ocorreu um problema ao importar credenciados.",
          position: "top-right",
          status: "error",
          isClosable: true,
        });
      },
    }
  );

  const handleAdd: SubmitHandler<FormData> = async (values) => {
    try {
      await add.mutateAsync(values);
    } catch {}
  };

  const handleCloseModal = () => {
    closeModal();
    reset();
  };

  useEffect(() => {
    if (file) {
      console.log("new file");
      readExcel(file);
    }
  }, [file]);

  return (
    <Modal
      size="md"
      isOpen={isOpen}
      onClose={handleCloseModal}
      closeOnOverlayClick={false}
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Importar</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack
            as="form"
            width="100%"
            p="4"
            spacing={["6", "8"]}
            flexDirection="column"
            align="flex-start"
            justify="center"
            onSubmit={handleSubmit(handleAdd)}
          >
            <ButtonExportExcel
              variant="link"
              isDisabled={dataExportExcel.length === 0}
              data={dataExportExcel}
              fileName={`modelo_importar_credenciado`}
            >
              Exportar modelo
            </ButtonExportExcel>
            <InputDocFile
              watch={watch}
              control={control}
              error={errors.file}
              {...register("file")}
            />
            <Flex justify="flex-end" w="100%">
              <HStack spacing="4" width="20em">
                <ButtonCancelSubmit onClick={handleCloseModal}>
                  Cancelar
                </ButtonCancelSubmit>
                <ButtonSubmit
                  isLoading={formState.isSubmitting}
                  isDisabled={!users}
                >
                  Salvar
                </ButtonSubmit>
              </HStack>
            </Flex>
          </VStack>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
};
