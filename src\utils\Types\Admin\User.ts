import { ActionLog } from "../Global";
import { PermissionProps } from "../Permissions"

export interface ListUserAdmin {
	secure_id: string
	name: string
	email: string
	type?: 'patient' | 'dependent' | 'doctor' | 'clinic' | 'lab' | 'hospital' | 'admin';
	birth_date: string
	legal_document_number: string
	cell?: string;
	show_accredited_in_app?: boolean;
	
	status?: 'active' | 'inactive' | 'punctual';

	isActive: boolean;

	partner?: {
		secureId: string;
		name: string;
	}
}

export type ShowUser = {
	secure_id: string
	email: string
	userInfo: {
		name: string
		legal_document_number: string;
		ddd_cell: string;
		cell: string;
		zip_code: string;
		street: string;
		number: string;
		complement: string;
		neighborhood: string;
		city: string;
		state: string;
		birth_date: string
		origin: string
	}
	avatar?: {
		secure_id: string
		url: string
		name: string
	}
	permissions: PermissionProps[]
	logs?: ActionLog[]

	isActive: boolean
}

export type UserFormData = {
	name: string
	email: string
	legal_document_number: string;
	cell: string
	zip_code: string;
	street: string;
	number: string;
	complement: string;
	neighborhood: string;
	city: string;
	state: string;
	birth_date: string
	password: string
	passwordConfirmation: string
	avatarSecureId: string
	userExists: boolean
	userSecureId: string
}
