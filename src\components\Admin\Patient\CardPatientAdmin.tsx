import {
	Flex,
	HStack,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { ListUserAdmin } from "~/utils/Types/Admin/User"
import { ShowHistory } from "~/components/global/Buttons/ShowHistory"
import { ActiveButton } from "~/components/global/Buttons/ActiveButton"
import { InactiveButton } from "~/components/global/Buttons/InactiveButton"

type CardPatientAdminProps = {
	patient: ListUserAdmin;
}

export const CardPatientAdmin: FC<CardPatientAdminProps> = ({ patient }) => {
	const toast = useToast()

	const userCanSeeDelete = useCan({
		permissions: ['patients_delete']
	})

	const userCanSeeEdit = useCan({
		permissions: ['patients_edit']
	})

	const inactivateUser = useMutation(async () => {
		return await api.delete(`/v1/admin/patients-inactivate/${patient.secure_id}`)
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['PatientsAdmin'])
			toast({
				title: response.data?.message || 'Paciente inativado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao inativar paciente.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})
	
	const activateUser = useMutation(async () => {
		return await api.post(`/v1/admin/patients-activate/${patient.secure_id}`, {
			isActive: true
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['PatientsAdmin'])
			toast({
				title: response.data?.message || 'Paciente ativado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao ativar paciente.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{patient.name}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patient?.partner?.name ? patient?.partner?.name : '-'}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patient.legal_document_number}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patient.cell}</Text>
			</Td>

			{(userCanSeeDelete || userCanSeeEdit) && (
				<Td>
					<Flex justify="flex-end">
						<HStack>
							{userCanSeeEdit && (
								<ShowHistory
									tooltipLabel="Visualizar paciente"
									linkHref={`/admin/patients/history/${patient.secure_id}`}
								/>
							)}
							
							{userCanSeeEdit && (
								<ButtonToEditItemList
									tooltipLabel="Editar paciente"
									linkHref={`/admin/patients/${patient.secure_id}`}
								/>
							)}

							{patient.isActive ? (
								userCanSeeDelete && (
									<InactiveButton
										inactiveFunction={inactivateUser}
										titlePopover="Inativar paciente"
										tooltipLabel="Inativar paciente"
										message="Deseja inativar esse paciente?"
									/>
								)
							) : (
								userCanSeeDelete && (
									<ActiveButton
										activeFunction={activateUser}
										titlePopover="Ativar paciente"
										tooltipLabel="Ativar paciente"
										message="Deseja ativar esse paciente?"
									/>
								)
							)}
						</HStack>
					</Flex>
				</Td>
			)}
		</Tr>
	)
}
