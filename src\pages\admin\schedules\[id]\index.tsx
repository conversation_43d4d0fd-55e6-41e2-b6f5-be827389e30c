import {
	AlertDialog,
	AlertDialogBody,
	AlertDialogContent,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogOverlay,
	Button,
	Checkbox,
	Divider,
	Flex,
	Grid,
	GridItem,
	HStack,
	Icon,
	Image,
	Link,
	Modal,
	ModalBody,
	ModalContent,
	ModalFooter,
	ModalHeader,
	ModalOverlay,
	Stack,
	Table,
	TableContainer,
	Tbody,
	Td,
	Text,
	Th,
	Thead,
	Tr,
	VStack,
	useDisclosure,
	useToast,
} from "@chakra-ui/react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { GetServerSideProps, NextPage } from "next";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";

import { RiPencilLine } from "react-icons/ri";
import { FaRegTrashAlt } from "react-icons/fa";
import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog";
import { ModalAddDisponibility } from "~/components/Admin/Schedule/ModalAddDisponibility";
import { Input } from "~/components/global/Form/Input";
import { InputNumberMask } from "~/components/global/Form/InputNumberMask";
import { setupApiClient } from "~/services/api";
import { api } from "~/services/apiClient";
import { queryClient } from "~/services/queryClient";
import {
	FormatDateForDayMonthYearUsingBars,
	FormatDateForHourMinutes,
} from "~/utils/Functions/FormatDates";
import {
	ScheduleDatesRequestsProps,
	ScheduleShowProps,
} from "~/utils/Types/Admin/Schedule";
import { Option } from "~/utils/Types/Global";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";
import { createObservationSchema, CreateObservationSchema } from "../../../../utils/schemas/createObservationSchema";
import { yupResolver } from "@hookform/resolvers/yup";
import { EditObservationSchema, editObservationSchema } from "../../../../utils/schemas/editObservationSchema";
import * as yup from "yup"
import { deleteObservationSchema, DeleteObservationSchema } from "../../../../utils/schemas/deleteObservationSchema";

interface ScheduleEditProps {
	scheduleData: ScheduleShowProps;
}

interface PaymentInfo {
	queryValue: string;
	queryValueSubsidy: string;
	queryValuePatient: string;
	paymentMethods: Option[];
}

interface EditSchedulePaymentProps {
	query_value?: number;
	query_value_subsidy?: number;
	query_value_patient?: number;
	schedule_id: string;
}

type EditObservationModalProps = {
	secureId: string;
	observation: string;
};

interface FormData extends PaymentInfo {
	scheduleSecureId: string;
	partnerSecureId: string;
	date: string;
}

type HandleScheduleDateProps = {
	status: "available" | "unavailable";
	scheduleSelectedSecureId: string[];
};

type HandleScheduleProps = {
	status: 'waiting_backoffice' | 'budget' | 'waiting_patient' | 'approved' | 'canceled_by_patient' | 'canceled_at_patient_request' | 'canceled_by_backoffice'
};

const ScheduleEdit: NextPage<ScheduleEditProps> = ({ scheduleData }) => {
	const { formState: { errors }, handleSubmit, watch, setValue, } = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(yup.object().shape({
			queryValue: yup.string().required("Valor obrigatório").test(
				'is-greater-than-subsidy',
				'Valor da consulta deve ser maior que o valor de subsídio', 
				//@ts-ignore
				(value, ctx) => Number(ctx.parent.queryValue) > Number(ctx.parent.queryValueSubsidy)
			),
		}))
	});

	const createObservationForm = useForm<CreateObservationSchema>(
		{ resolver: yupResolver(createObservationSchema),
			defaultValues: {
				observation: ''
			}
		}
	);
	const editObservationForm = useForm<EditObservationSchema>(
		{ resolver: yupResolver(editObservationSchema),
			defaultValues: {
				observation: '',
				observationSecureId: '',
			}
		}
	);
	const deleteObservationForm = useForm<DeleteObservationSchema>(
		{ resolver: yupResolver(deleteObservationSchema),
			defaultValues: {
				observation: '',
				observationSecureId: '',
			}
		}
	);

	const [queryValue, setQueryValue ] = useState(0)
	const [queryValueSubsidy, setQueryValueSubsidy ] = useState(0)
	const [queryValuePatient, setQueryValuePatient ] = useState(0)

	const watchQueryValue = watch('queryValue')
	const watchQueryValueSubsidy = watch('queryValueSubsidy')

	const [selectedSchedule, setSelectedSchedule] =
		useState<EditSchedulePaymentProps>();

	const [selectedObservation, setSelectedObservation] =
		useState<EditObservationModalProps>();

	const toast = useToast();

	const { data: dataSchedule } = useQuery(
		["schedules", scheduleData.secure_id],
		async () => {
			const response = await api.get(`/v1/admin/schedules/${scheduleData.secure_id}`);
			return response.data as ScheduleShowProps;
		}
	);

	const scheduleDatesRequests = useMemo(() => {
		if (!dataSchedule) {
			return scheduleData.scheduleDatesRequests
		}

		return dataSchedule.scheduleDatesRequests
	}, [dataSchedule])

	const observationRequest = useMemo(() => {
		if (!dataSchedule) {
			return scheduleData.observations
		}

		return dataSchedule.observations
	}, [dataSchedule])

	const datesFormatted = useMemo(() => {
		return Object.values(scheduleDatesRequests)
	}, [dataSchedule])

	const [checkedItems, setCheckedItems] = useState(
		datesFormatted.map((item) => false)
	);

	const observations = useMemo(() => {
		return observationRequest
	}, [dataSchedule])

	const [checkedObservations, setCheckedObservations] = useState(
		observations?.map((item) => false)
	)

	const {
		isOpen: isOpenAddNewTime,
		onOpen: onOpenAddNewTime,
		onClose: onCloseAddNewTime,
	} = useDisclosure();
	const {
		isOpen: isOpenEditPayment,
		onOpen: onOpenEditPayment,
		onClose: onCloseEditPayment,
	} = useDisclosure();

	const { 
		isOpen: isOpenEditObservationModal,
		onOpen: onOpenEditObservationModal,
		onClose: onCloseEditObservationModal, 
	} = useDisclosure();
	
	const { 
		isOpen: isOpenCreateObservationModal,
		onOpen: onOpenCreateObservationModal,
		onClose: onCloseCreateObservationModal,
	} = useDisclosure();
	
	const { 
		isOpen: isOpenDeleteObservationModal,
		onOpen: onOpenDeleteObservationModal,
		onClose: onCloseDeleteObservationModal,
	} = useDisclosure();

	const {
		isOpen: isOpenAvailable,
		onOpen: onOpenAvailable,
		onClose: onCloseAvailable,
	} = useDisclosure();
	const {
		isOpen: isOpenUnavailable,
		onOpen: onOpenUnavailable,
		onClose: onCloseUnavailable,
	} = useDisclosure();
	const refAvailable = useRef(null);
	const refUnavailable = useRef(null);

	const allChecked = useMemo(() => {
		return checkedItems.every(Boolean);
	}, [checkedItems]);

	const allObservationChecked = useMemo(() => {
		if (checkedObservations.length === 0) {
			return false;
		}

		return checkedObservations.every(Boolean);
	}, [checkedObservations])

	const isIndeterminate = useMemo(() => {
		return checkedItems.some(Boolean) && !allChecked;
	}, [checkedItems, allChecked]);

	const isObservationIndeterminate = useMemo(() => {
		return checkedObservations.some(Boolean) && !allObservationChecked;
	}, [checkedObservations, allObservationChecked]);

	const schedulesSelected = useMemo(() => {
		const scheduleSelected = datesFormatted
			.map((item, index) => ({
				...item,
				isChecked: checkedItems[index],
			}))
			.filter((item) => item.isChecked);
		return scheduleSelected;
	}, [checkedItems, datesFormatted]);

	const isAvailabilityAllSelected = useMemo(() => {
		return !!schedulesSelected.find((item) => item.date_type === "period");
	}, [schedulesSelected]);

	const handleScheduleDate = useMutation(
		async (data: HandleScheduleDateProps) => {
			return api.post(`/v1/admin/schedules-dates`, data);
		},
		{
			onSuccess: () => {
				queryClient.invalidateQueries(["schedules"]);
				queryClient.invalidateQueries(["ScheduleAdmin"])
				onCloseAvailable();
				onCloseUnavailable();
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Erro ao alterar disponibilidade do horário.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			},
		}
	);

	const handleSchedule = useMutation(
		async (data: HandleScheduleProps) => {
			return api.put(`/v1/admin/schedules/${scheduleData.secure_id}`, data);
		},
		{
			onSuccess: () => {
				queryClient.invalidateQueries(["schedules"]);
				queryClient.invalidateQueries(["ScheduleAdmin"]);
				toast({
					title: "Solicitação de agendamento atualizada com sucesso.",
					position: "top-right",
					status: "success",
					isClosable: true,
				});
				history.back();
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Erro ao alterar a solicitação de agendamento.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			},
		}
	);

	const handleFinish = async () => {
		try {
			if (!scheduleData) {
				toast({
					title: "Solicitação de agendamento não encontrada.",
					position: "top-right",
					status: "error",
					isClosable: true,
				});
				return;
			}
			if (
				scheduleDatesRequests.find(
					(item) => item.status === "to_check"
				)
			) {
				toast({
					title: "Existem horários para serem checados.",
					position: "top-right",
					status: "error",
					isClosable: true,
				});
				return;
			}

			if (
				!scheduleDatesRequests.find(
					(item) => item.status === "available"
				)
			) {
				toast({
					title: "Não existe horários disponíveis.",
					position: "top-right",
					status: "error",
					isClosable: true,
				});
				return;
			}
			await handleSchedule.mutateAsync({ status: "waiting_patient" });
		} catch { }
	};

	const handleMakeAvailable = async () => {
		try {
			await handleScheduleDate.mutateAsync({
				status: "available",
				scheduleSelectedSecureId: schedulesSelected.map(
					(item) => item.secure_id
				),
			});
		} catch { }
	};

	const handleMakeUnavailable = async () => {
		try {
			await handleScheduleDate.mutateAsync({
				status: "unavailable",
				scheduleSelectedSecureId: schedulesSelected.map(
					(item) => item.secure_id
				),
			});
		} catch { }
	};

	const checkDateType = useCallback((data: ScheduleDatesRequestsProps) => {
		if (data.date_type === "period") {
			switch (data.value) {
				case "morning":
					return "Manhã";

				case "afternoon":
					return "Tarde";

				case "night":
					return "Noite";
				default:
					return "";
			}
		}
		return FormatDateForHourMinutes(data.date);
	}, []);

	const checkStatus = useCallback((data: ScheduleDatesRequestsProps) => {
		switch (data.status) {
			case "to_check":
				return { text: "Checar", color: "orange.400" };

			case "unavailable":
				return { text: "Indisponível", color: "red" };

			case "available":
				return { text: "Disponível", color: "green" };
			default:
				return { text: "", color: "" };
		}
	}, []);

	const handleUpdateSchedule = useMutation(
		async (data: PaymentInfo) => {
			return api.put(
				`/v1/admin/schedules-data/${selectedSchedule?.schedule_id}`,
				{
					...data,
				}
			);
		},
		{
			onSuccess: () => {
				queryClient.invalidateQueries(["schedules"]);
				queryClient.invalidateQueries(["ScheduleAdmin"]);
				toast({
					title: "Horário adicionado com sucesso.",
					position: "top-right",
					status: "success",
					isClosable: true,
				});
				onCloseEditPayment();
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message || "Erro ao adicionar o horário.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			},
		}
	);

	// const definePatientValue = () => {
	// 	if (queryValueSubsidy === '') return queryValue
	// 	const patientValue = Number(queryValue) - Number(queryValueSubsidy)
	// 	return patientValue.toFixed(2)
	// }

	const handleEdit = async (data: PaymentInfo) => {
		try {
			await handleUpdateSchedule.mutateAsync({ ...data });
		} catch { }
	};

	const onOpenEditPaymentInfosModal = (data: EditSchedulePaymentProps) => {
		setQueryValue(data.query_value ? data.query_value / 100 : 0)
    setQueryValueSubsidy(data.query_value_subsidy ? data.query_value_subsidy / 100 : 0)
    setQueryValuePatient(data.query_value_patient ? data.query_value_patient / 100 : 0) // : queryValue (?)
		setSelectedSchedule(data);
		onOpenEditPayment();
	};

	const handleOpenEditObservationModal = (data: EditObservationModalProps) => {
		onOpenEditObservationModal();


		editObservationForm.setValue("observation", data.observation);
		editObservationForm.setValue("observationSecureId", data.secureId);
	}

	const handleSubmitNewObservation = useCallback(async (data: CreateObservationSchema) => {
		try {
			const createObservationObject = {
				observation: data.observation,
				scheduleSecureId: scheduleData.secure_id,
				from: 'schedule'
			};
	
			await api.post('/v1/admin/observations', 
				createObservationObject
			);

			queryClient.invalidateQueries({
				queryKey: ["schedules"]
			});

			queryClient.invalidateQueries({
				queryKey: ["ActionLogsAdmin"]
			});

			toast({
				title: "Observação adicionado com sucesso.",
				position: "top-right",
				status: "success",
				isClosable: true,
			});

			createObservationForm.reset();
			onCloseCreateObservationModal();
		} catch (error) {
			if (error instanceof AxiosError) {
				const message = error?.request?.data?.message;

				toast({
					title:
						message || "Erro ao adicionar nova observação.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			}
		}
	}, []);

	const handleCloseCreateObservation = useCallback(() => {
		onCloseCreateObservationModal()
	}, []);

	const handleEditObservation = useCallback(async (data: EditObservationSchema) => {
		try {
			const editObservationObject = {
				observation: data.observation,
				from: 'schedule'
			};

			await api.put(`/v1/admin/observations/${data.observationSecureId}`, 
				editObservationObject
			);

			queryClient.invalidateQueries({
				queryKey: ["schedules"]
			});

			queryClient.invalidateQueries({
				queryKey: ["ActionLogsAdmin"]
			});

			toast({
				title: "Observação editada com sucesso.",
				position: "top-right",
				status: "success",
				isClosable: true,
			});

			onCloseEditObservationModal();
		} catch (error) {
			if (error instanceof AxiosError) {
				const message = error?.request?.data?.message;

				toast({
					title:
						message || "Erro ao editar observação.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			}
		}
	}, []);

	function handleErrorEditObservation(data: any) {
		// console.log("error: ", data)
	}

	const handleOpenDeleteObservationModal = (data: DeleteObservationSchema) => {
		onOpenDeleteObservationModal();


		deleteObservationForm.setValue("observation", data.observation);
		deleteObservationForm.setValue("observationSecureId", data.observationSecureId);
	}

	const handleDeleteObservation = useCallback(async (data: DeleteObservationSchema) => {
		try {
			await api.delete(`/v1/admin/observations/${data.observationSecureId}/schedule`);

			queryClient.invalidateQueries({
				queryKey: ["schedules"]
			});

			queryClient.invalidateQueries({
				queryKey: ["ActionLogsAdmin"]
			});

			toast({
				title: "Observação excluída com sucesso.",
				position: "top-right",
				status: "success",
				isClosable: true,
			});
			
			onCloseDeleteObservationModal();
		} catch (error) {
			if (error instanceof AxiosError) {
				const message = error?.request?.data?.message;

				toast({
					title:
						message || "Erro ao excluir observação.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				});
			}
		}
	}, []);

	useEffect(() => {
    if (selectedSchedule) {
      setQueryValue(selectedSchedule.query_value ? selectedSchedule.query_value / 100 : 0);
      setValue('queryValue', selectedSchedule.query_value ? String(selectedSchedule.query_value / 100) : '0');
      setQueryValueSubsidy(selectedSchedule.query_value_subsidy ? selectedSchedule.query_value_subsidy / 100 : 0);
      setValue('queryValueSubsidy', selectedSchedule.query_value_subsidy ? String(selectedSchedule.query_value_subsidy / 100) : '0');
      setQueryValuePatient(selectedSchedule.query_value_patient ? selectedSchedule.query_value_patient / 100 : (selectedSchedule.query_value ? selectedSchedule.query_value / 100 : 0));
    }
  }, [selectedSchedule, setValue]);

	useEffect(() => {
    const patientValue = Number(watchQueryValue) - Number(watchQueryValueSubsidy);
    setQueryValuePatient(patientValue);
    setValue('queryValuePatient', String(patientValue));
  }, [watchQueryValue, watchQueryValueSubsidy, setValue]);

	return (
		<VStack spacing="4" layerStyle="container">
			<Stack w="100%" background="white" rounded="md" shadow="md" padding={4}>
				<Grid
					templateColumns={{
						sm: "repeat(4, 1fr)",
						md: "repeat(8, 1fr)",
						lg: "repeat(10, 1fr)",
						xl: "repeat(12, 1fr)",
						"2xl": "repeat(12, 1fr)",
					}}
					gap={6}
				>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
						<Text fontSize="xs">Solicitação</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">
								{scheduleData?.type_consult !== "exam"
									? "Consulta com:"
									: "Exame:"}
							</Text>
							<Text>
								{scheduleData?.specialty?.name}
								{scheduleData?.exam?.name}
							</Text>
						</HStack>
					</GridItem>

					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
						<Text fontSize="xs">Paciente</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">
								Nome:{" "}
							</Text>
							<Text>{scheduleData?.patient.userInfo.name}</Text>
						</HStack>
						{scheduleData?.patient.userInfo.phone && (
							<HStack>
								<Text fontSize="sm" fontWeight="medium">
									Telefone:{" "}
								</Text>
								<Text>
									({scheduleData?.patient.userInfo.ddd_phone}) -{" "}
									{scheduleData?.patient.userInfo.phone}
								</Text>
							</HStack>
						)}
						{scheduleData?.patient.userInfo.cell && (
							<HStack>
								<Text fontSize="sm" fontWeight="medium">
									Celular:{" "}
								</Text>
								<Text>
									({scheduleData?.patient.userInfo.ddd_cell}) -{" "}
									{scheduleData?.patient.userInfo.cell}
								</Text>
							</HStack>
						)}
					</GridItem>

					{scheduleData?.patient?.parent && (
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
							<Stack>
								<Text fontSize="xs">Responsável</Text>
								<Divider />
								<HStack>
									<Text fontSize="sm" fontWeight="medium">
										Nome:{" "}
									</Text>
									<Text>{scheduleData?.patient?.parent.userInfo.name}</Text>
								</HStack>
								{scheduleData?.patient?.parent.userInfo.phone && (
									<HStack>
										<Text fontSize="sm" fontWeight="medium">
											Telefone:{" "}
										</Text>
										<Text>
											({scheduleData?.patient?.parent.userInfo.ddd_phone}) -{" "}
											{scheduleData?.patient?.parent.userInfo.phone}
										</Text>
									</HStack>
								)}
								{scheduleData?.patient?.parent.userInfo.cell && (
									<HStack>
										<Text fontSize="sm" fontWeight="medium">
											Celular:{" "}
										</Text>
										<Text>
											({scheduleData?.patient?.parent.userInfo.ddd_cell}) -{" "}
											{scheduleData?.patient?.parent.userInfo.cell}
										</Text>
									</HStack>
								)}
							</Stack>
						</GridItem>
					)}

					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
						<Text fontSize="xs">Local</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">
								Bairro:{" "}
							</Text>
							<Text>{scheduleData?.neighborhood}</Text>
						</HStack>
						<HStack>
							<Text fontSize="sm" fontWeight="medium">
								Cidade:{" "}
							</Text>
							<Text>{scheduleData?.city}</Text>
						</HStack>
						<HStack>
							<Text fontSize="sm" fontWeight="medium">
								Estado:{" "}
							</Text>
							<Text>{scheduleData?.state}</Text>
						</HStack>
					</GridItem>

					{scheduleData?.uploads && scheduleData?.uploads?.length > 0 && (
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
							<Text fontSize="xs">Solicitação de Exame</Text>

							<Divider />

							{scheduleData?.uploads.map(item => (
								<Link href={item.url} target="_blank" key={item.url}>
									<Image src={item.url} w={14} h={16} />
								</Link>
							))}
						</GridItem>
					)}
				</Grid>
			</Stack>

			<Stack flexDirection="row" width="100%" justify="space-between">
				<Button colorScheme="blue" onClick={onOpenCreateObservationModal}>
					Adicionar Observação
				</Button>{" "}
			</Stack>

			<Stack w="100%" background="white" rounded="md" shadow="md" padding={4}>
				<TableContainer>
					<Table whiteSpace="pre-wrap" variant="simple">
						<Thead>
							<Tr>
								{/* <Th paddingY="4" paddingX="2">
									<Checkbox
										isChecked={allObservationChecked}
										isIndeterminate={isObservationIndeterminate}
										size="lg"
										onChange={(e) => {
											setCheckedObservations(
												observations.map((observation) => e.target.checked)
											);
										}}
									/>
								</Th> */}

								<Th paddingY="4" paddingX="2">Observação</Th>

								<Th paddingY="4" paddingX="2" width='15%' textAlign='center'>Ações</Th>
							</Tr>
						</Thead>

						<Tbody>
							{observationRequest && observationRequest.length > 0 && (
								observationRequest.map((item, index) => (
									<Tr key={`${item.secureId} - ${index}`}>
										{/* <Td paddingY="4" paddingX="2">
											<Checkbox
												isChecked={checkedObservations[index]}
												onChange={(e) => {
													setCheckedObservations(
														observations.map((observation, newIndex) =>
															newIndex === index
																? e.target.checked
																: checkedObservations[newIndex]
														)
													);
												}}
											/>
										</Td> */}
										<Td paddingY="4" paddingX="2">
											{item.observation ? item.observation : "Sem observação"}
										</Td>

										<Td paddingY="4" paddingX="2" mx='auto'>
											<HStack gap={2} justifyContent='center'>
												<Button
													size="sm"
													fontSize="sm"
													colorScheme="telegram"
													title="Editar"
													onClick={() =>
														handleOpenEditObservationModal({ observation: item.observation, secureId: item.secureId})
													}
												>
													<Icon as={RiPencilLine } fontSize="20" />
												</Button>
												
												<Button
													size="sm"
													fontSize="sm"
													colorScheme="red"
													title="Excluir"
													onClick={() =>
														handleOpenDeleteObservationModal(
															{ 
																observation: item.observation, 
																observationSecureId: item.secureId
															}
														)
													}
												>
													<Icon as={FaRegTrashAlt} fontSize="20" />
												</Button>
											</HStack>
										</Td>
									</Tr>
								)
							))}
						</Tbody>
					</Table>
				</TableContainer>
			</Stack>

			<Stack flexDirection="row" width="100%" justify="space-between">
				<Button colorScheme="blue" onClick={onOpenAddNewTime}>
					Adicionar Horário
				</Button>{" "}

				{/*  cria uma schedule_dates_requests*/}
				<Stack flexDirection="row" justify="flex-end">
					<Button colorScheme="red" onClick={onOpenUnavailable}>
						Cancelar Horário
					</Button>
					<Button
						colorScheme="green"
						onClick={onOpenAvailable}
						isDisabled={isAvailabilityAllSelected}
					>
						Disponibilizar Horário
					</Button>
				</Stack>
			</Stack>

			<Stack w="100%" background="white" rounded="md" shadow="md" padding={4}>
				<Flex></Flex>
				<TableContainer>
					<Table whiteSpace="pre-wrap" variant="simple">
						<Thead>
							<Tr>
								<Th paddingY="4" paddingX="2">
									<Checkbox
										isChecked={allChecked}
										isIndeterminate={isIndeterminate}
										size="lg"
										onChange={(e) => {
											setCheckedItems(
												datesFormatted.map((scheduleDate) => e.target.checked)
											);
										}}
									/>
								</Th>
								<Th paddingY="4" paddingX="2">Médico / Clinica / Laboratório</Th>
								<Th paddingY="4" paddingX="2">Status</Th>
								<Th paddingY="4" paddingX="2">Telefone</Th>
								<Th paddingY="4" paddingX="2">Data</Th>
								<Th paddingY="4" paddingX="2">Horário</Th>
								<Th paddingY="4" paddingX="2">Disponibilidade</Th>
								<Th paddingY="4" paddingX="2">Ações</Th>
							</Tr>
						</Thead>
						<Tbody>
							{scheduleDatesRequests.map((item, index) => (
								<Tr key={item.secure_id}>
									<Td paddingY="4" paddingX="2">
										<Checkbox
											isChecked={checkedItems[index]}
											onChange={(e) => {
												setCheckedItems(
													datesFormatted.map((date, newIndex) =>
														newIndex === index
															? e.target.checked
															: checkedItems[newIndex]
													)
												);
											}}
										/>
									</Td>
									<Td paddingY="4" paddingX="2">
										{item.partner && item.partner.userInfo
											? item.partner.userInfo.name
											: "helloMed"}
									</Td>
									<Td paddingY="4" paddingX="2">
									{item.partner && item.partner.userInfo.status 
											? item.partner.userInfo.status === 'active' 
											? 'Ativo' 
											: item.partner.userInfo.status === 'inactive' 
												? 'Inativo' 
												: item.partner.userInfo.status === 'punctual'
												? 'Pontual' 
												: 'Status desconhecido'
											: 'Sem status' 
										}

									</Td>
									<Td paddingY="4" paddingX="2">
										{item.partner &&
											item.partner.userInfo &&
											(item.partner.userInfo.cell ||
												item.partner.userInfo.phone) &&
											`(${!!item.partner.userInfo.ddd_phone
												? item.partner.userInfo.ddd_phone
												: item.partner.userInfo.ddd_cell
											}) ${!!item.partner.userInfo.phone
												? item.partner.userInfo.phone
												: item.partner.userInfo.cell
											}`}
									</Td>
									<Td paddingY="4" paddingX="2">{FormatDateForDayMonthYearUsingBars(item.date)}</Td>
									<Td paddingY="4" paddingX="2">{checkDateType(item)}</Td>
									<Td color={checkStatus(item).color}>
										{checkStatus(item).text}
									</Td>
									<Td paddingY="4" paddingX="2">
										<Button
											size="sm"
											fontSize="sm"
											colorScheme="telegram"
											title="Editar"
											onClick={() =>
												onOpenEditPaymentInfosModal({
													query_value: item?.query_value,
													query_value_subsidy: item?.query_value_subsidy,
													query_value_patient: item?.query_value_patient ? item?.query_value_patient : 0,
													schedule_id: item?.secure_id,
												})
											}
										>
											<Icon as={RiPencilLine} fontSize="20" />
										</Button>
									</Td>
								</Tr>
							))}
						</Tbody>
					</Table>
				</TableContainer>
			</Stack>

			<Stack flexDirection="row" width="100%" justify="flex-end">
				<Button
					colorScheme="facebook"
					onClick={handleFinish}
					isLoading={handleSchedule.isLoading}
				>
					Finalizar
				</Button>
			</Stack>

			<Modal isOpen={isOpenEditPayment} onClose={onOpenEditPayment}>
				<ModalOverlay />
				<ModalContent>
					<ModalHeader>Editar informações de pagamento</ModalHeader>
					<ModalBody>
						<Stack backgroundColor="white">
							<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
								<GridItem colSpan={12}>
									<InputNumberMask
										setValue={setValue}
										placeholder="R$ 0,00"
										prefix="R$ "
										error={errors.queryValue}
										value={
											!!selectedSchedule?.query_value
												? selectedSchedule.query_value / 100
												: ""
										}
										label="Valor Hellomed"
										name="queryValue"
									/>
								</GridItem>
								<GridItem colSpan={12}>
									<InputNumberMask
										setValue={setValue}
										placeholder="R$ 0,00"
										prefix="R$ "
										error={errors.queryValueSubsidy}
										value={
											!!selectedSchedule?.query_value_subsidy
												? selectedSchedule.query_value_subsidy / 100
												: ""
										}
										label="Valor Subsidiado"
										name="queryValueSubsidy"
									/>
								</GridItem>
								<GridItem colSpan={12}>
									<InputNumberMask
										isReadOnly
										cursor='not-allowed'
										setValue={setValue}
										placeholder="R$ 0,00"
										prefix="R$ "
										error={errors.queryValuePatient}
										value={queryValuePatient}
										label="Valor Paciente"
										name="queryValuePatient"
									/>
								</GridItem>
							</Grid>
						</Stack>
					</ModalBody>

					<ModalFooter>
						<Button variant="ghost" onClick={onCloseEditPayment}>
							Cancelar
						</Button>
						<Button
							colorScheme="blue"
							mr={3}
							onClick={handleSubmit(handleEdit)}
						>
							Salvar
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>
			
			{/* Add observation */}
			<Modal isOpen={isOpenCreateObservationModal} onClose={handleCloseCreateObservation}>
				<ModalOverlay />
				<ModalContent>
					<ModalHeader>Adicionar observação</ModalHeader>

					<ModalBody>
						<Stack backgroundColor="white">
							<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
								<GridItem colSpan={12}>
									<Input
										placeholder="Nova observação"
										error={errors.queryValue}
										label="Observação"
										{...createObservationForm.register("observation")}
										isDisabled={createObservationForm.formState.isSubmitting}
									/>
								</GridItem>
							</Grid>
						</Stack>
					</ModalBody>

					<ModalFooter>
						<Button variant="ghost" onClick={handleCloseCreateObservation}>
							Cancelar
						</Button>

						<Button
							colorScheme="blue"
							mr={3}
							onClick={
								createObservationForm.handleSubmit(handleSubmitNewObservation)
							}
							isLoading={createObservationForm.formState.isSubmitting}
						>
							Salvar
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>
			
			{/* Edit observation */}
			<Modal isOpen={isOpenEditObservationModal} onClose={onOpenEditObservationModal}>
				<ModalOverlay />
				<ModalContent>
					<ModalHeader>Editar observações</ModalHeader>

					<ModalBody>
						<Stack backgroundColor="white">
							<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
								<GridItem colSpan={12}>
									<Input
										placeholder="Editar observação"
										error={errors.queryValue}
										label="Edição de observação"
										{...editObservationForm.register("observation")}
										isDisabled={editObservationForm.formState.isSubmitting}
									/>
								</GridItem>
							</Grid>
						</Stack>
					</ModalBody>

					<ModalFooter>
						<Button variant="ghost" onClick={onCloseEditObservationModal}>
							Cancelar
						</Button>
						<Button
							colorScheme="blue"
							mr={3}
							onClick={editObservationForm.handleSubmit(handleEditObservation, handleErrorEditObservation)}
							isLoading={editObservationForm.formState.isSubmitting}
						>
							Editar
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>

			{/* Delete observation */}
			<Modal isOpen={isOpenDeleteObservationModal} onClose={onCloseDeleteObservationModal}>
				<ModalOverlay />
				<ModalContent>
					<ModalHeader>Apagar observações</ModalHeader>

					<ModalBody>
						<Stack backgroundColor="white">
							<Grid templateColumns={"repeat(12, 1fr)"} gap={6}>
								<GridItem colSpan={12}>
									<Text>Tem certeza que deseja excluir a observação?</Text>
									<Text mt={2} p={2} borderRadius={6} backgroundColor='#D8D8D8'>{deleteObservationForm.getValues('observation')}</Text>
								</GridItem>
							</Grid>
						</Stack>
					</ModalBody>

					<ModalFooter>
						<Button variant="ghost" onClick={onCloseDeleteObservationModal}>
							Cancelar
						</Button>
						<Button
							colorScheme="red"
							mr={3}
							onClick={deleteObservationForm.handleSubmit(handleDeleteObservation)}
							isLoading={deleteObservationForm.formState.isSubmitting}
						>
							Excluir
						</Button>
					</ModalFooter>
				</ModalContent>
			</Modal>

			<AlertDialog
				isOpen={isOpenAvailable}
				leastDestructiveRef={refAvailable}
				onClose={onCloseAvailable}
			>
				<AlertDialogOverlay>
					<AlertDialogContent>
						<AlertDialogHeader fontSize="lg" fontWeight="bold">
							Disponibilizar Horário
						</AlertDialogHeader>

						<AlertDialogBody>
							Os horários selecionados serão disponibilizados para o usuário.
						</AlertDialogBody>

						<AlertDialogFooter>
							<Button colorScheme="red" onClick={onCloseAvailable}>
								Cancelar
							</Button>
							<Button
								isLoading={handleScheduleDate.isLoading}
								colorScheme="green"
								onClick={handleMakeAvailable}
								ml={3}
							>
								Disponibilizar
							</Button>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialogOverlay>
			</AlertDialog>

			<AlertDialog
				isOpen={isOpenUnavailable}
				leastDestructiveRef={refUnavailable}
				onClose={onCloseUnavailable}
			>
				<AlertDialogOverlay>
					<AlertDialogContent>
						<AlertDialogHeader fontSize="lg" fontWeight="bold">
							Confirmar o cancelamento de horário
						</AlertDialogHeader>

						<AlertDialogBody>
							Os horários selecionados serão cancelados e não serão mais
							exibidos para o usuário.
						</AlertDialogBody>

						<AlertDialogFooter>
							<Button colorScheme="red" onClick={onCloseUnavailable}>
								Cancelar
							</Button>
							<Button
								isLoading={handleScheduleDate.isLoading}
								colorScheme="green"
								onClick={handleMakeUnavailable}
								ml={3}
							>
								Confirmar Cancelamento
							</Button>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialogOverlay>
			</AlertDialog>

			<ModalAddDisponibility
				isOpen={isOpenAddNewTime}
				onClose={onCloseAddNewTime}
				typeConsult={scheduleData?.type_consult}
				scheduleSecureId={scheduleData.secure_id}
			/>

			{/* <ModalEditObservation 
				isOpen={isOpenEditObservationModal}
				onClose={onCloseEditObservationModal}
			/> */}

			<ListActionLog
				layerStyle="card"
				changedSecureId={scheduleData.secure_id}
				type="schedule"
			/>
		</VStack>
	);
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		const api = setupApiClient(ctx)

		const { data } = await api.get(`/v1/admin/schedules/${id}`)

		if (data.status === "waiting_patient" ) {
			return {
				redirect: {
					destination: `/admin/schedules/${id}/approve`,
					permanent: false
				}
			}
		} else if (data.status !== "waiting_backoffice" && data.status !== "budget" && data.status !== "in_accreditation" && data.status !== "waiting_backoffice_network") {
			return {
				redirect: {
					destination: `/admin/schedules/${id}/view`,
					permanent: false
				}
			}
		}

		return {
			props: {
				scheduleData: data,
			},
		};
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["schedule_edit"],
	}
);

export default ScheduleEdit;
