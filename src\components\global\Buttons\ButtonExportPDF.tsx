import { Button, ButtonProps, Icon } from "@chakra-ui/react"

import { FC, ReactNode, useCallback } from "react"

import { PiFilePdfLight } from "react-icons/pi"

import { ExportDataToPDF } from "~/utils/Functions/DocFunctions"

import { TableExportExcel } from "~/utils/Types/Global"

interface ButtonExportPDFProps extends ButtonProps {
	children: ReactNode
	data: TableExportExcel[]
	fileName: string
	orientationPdf?: 'p' | 'l'
}

export const ButtonExportPDF: FC<ButtonExportPDFProps> = ({ children, data, fileName, orientationPdf, ...rest }) => {
	const exportToExcel = useCallback(() => {
		ExportDataToPDF({ data, fileName, orientationPdf })
	}, [data])

	return (
		<Button
			as="a"
			size="md"
			fontSize="md"
			colorScheme="orange"
			cursor="pointer"
			leftIcon={<Icon as={PiFilePdfLight} fontSize="20" />}
			onClick={exportToExcel}
			{...rest}
		>
			{children}
		</Button>
	)
}
