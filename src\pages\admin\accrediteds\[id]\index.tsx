import {
  VStack,
  useToast,
  Box,
  Flex,
  HStack,
  Grid,
  GridItem,
  Divider,
} from "@chakra-ui/react";
import { GetServerSideProps, NextPage } from "next";

import * as yup from "yup";
import {
  FormState,
  SubmitHandler,
  UseFormClearErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
  useForm,
} from "react-hook-form";

import { useMutation } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { yupResolver } from "@hookform/resolvers/yup";

import { api } from "~/services/apiClient";
import { setupApiClient } from "~/services/api";
import { queryClient } from "~/services/queryClient";
import { Option, typesDoctors, typesOfCares, typesStatus } from "~/utils/Types/Global";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";
import { ShowUser, UserFormData } from "~/utils/Types/Admin/User";

import { Input } from "~/components/global/Form/Input";
import { InputRadio } from "~/components/global/Form/InputRadio";
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit";
import { InputSelectMult } from "~/components/global/Form/InputSelectMult";
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit";
import { InputCreatableSelect } from "~/components/global/Form/InputCreatableSelect";
import { FormatDateForYearMonthDay } from "~/utils/Functions/FormatDates";
import { InputPassword } from "~/components/global/Form/InputPassword";
import { useEffect } from "react";
import { InputImage } from "~/components/global/Form/ImputImage";
import { InputMask } from "~/components/global/Form/InputMask";
import { InputDate } from "~/components/global/Form/InputDate";
import { AccreditedTabsPanel } from "~/components/Admin/Accredited/AccreditedTabsPanel";
import { InputNumberMask } from "~/components/global/Form/InputNumberMask";
import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog";
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect";
import { cnpjValidation } from "~/utils/Validator/validateCnpj";
import { cpfValidation } from "~/utils/Validator/validateCpf";

export type ShowDoctor = ShowUser & {
  type: string;
  userInfo: {
    zip_code: string;
    street: string;
    number: string;
    complement: string;
    neighborhood: string;
    city: string;
    state: string;
    advice_register: string;
    payment_methods: string;
    type_of_care: string;
    status: string;
    query_value: number;
    accredited_value: number;
  };
  specialties: {
    secure_id: string;
    name: string;
  }[];
  exams: {
    secure_id: string
    name: string
  }[]
  accreditedGroups: {
    secure_id: string
    name: string
  }[]
};

type FormData = UserFormData & {
  adviceRegister: string;
  type: string;
  typeOfCare: string;
  status: string;
  groupSecureId: Option
  paymentMethods: Option[];
  specialtiesSecureIds: Option[];
  examsSecureIds: Option[];
  queryValue: number;
  accreditedValue: number;
};

const FormSchema = yup.object().shape({
  userExists: yup.boolean(),
  name: yup.string().required("Nome obrigatório"),
  email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
  password: yup.string().when("password", {
    is: (value: string) => !!value && value.length > 0,
    then: (schema) => schema
      .required("Senha obrigatória")
      .min(8, "No mínimo 8 caracteres")
      .matches(
        /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/])[A-Za-z\d!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/]{6,}$/,
        'A senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.'
      )
  }),
  passwordConfirmation: yup
    .string()
    .oneOf([yup.ref("password")], "As senha precisam ser iguais"),
  legal_document_number: yup
  .string()
  .required("CPF obrigatório"),
  cell: yup.string().required("Celular obrigatório"),
  adviceRegister: yup.string().required("Registro obrigatório"),
  type: yup.string().required("Tipo"),
  typeOfCare: yup.string().required("Tipo de atendimento"),
  status: yup.string().required("Status"),
  paymentMethods: yup
    .array()
    .of(yup.object().shape({}))
    .required("Métodos de pagamento obrigatório"),
  queryValue: yup.number().required("Valor do pagamento obrigatório"),
}, [
  ["password", "password"]
]);
interface DoctorsEditProps {
  specialtiesOptions: Option[];
  examsOptions: Option[]
  accredited: ShowDoctor;
}
const DoctorsEdit: NextPage<DoctorsEditProps> = ({
  specialtiesOptions,
  examsOptions,
  accredited,
}) => {
  const toast = useToast();

  console.log(accredited)

  const getCep = async (value: string) => {
    if (value && value.length === 8) {
      api
        .get(`https://brasilapi.com.br/api/cep/v2/${value}`)
        .then((response) => {
          const data: any = response.data;
          setValue("street", data.street);
          setValue("city", data.city);
          setValue("neighborhood", data.neighborhood);
          setValue("state", data.state);
        })
        .catch(() => {
          // setBairro('')
          // setCity('')
        });
    }
  };

  const {
    register,
    formState,
    formState: { errors },
    handleSubmit,
    watch,
    setValue,
    clearErrors,
    control,
  } = useForm<FormData>({
    //@ts-ignore
    resolver: yupResolver(FormSchema),
    defaultValues: {
      name: accredited.userInfo.name,
      email: accredited.email,
      legal_document_number: accredited.userInfo.legal_document_number,
      cell: accredited.userInfo.ddd_cell
        ? `${accredited.userInfo.ddd_cell}${accredited.userInfo.cell}`
        : "",
      zip_code: accredited.userInfo.zip_code,
      street: accredited.userInfo.street,
      number: accredited.userInfo.number,
      complement: accredited.userInfo.complement,
      neighborhood: accredited.userInfo.neighborhood,
      city: accredited.userInfo.city,
      state: accredited.userInfo.state,
      birth_date: accredited.userInfo.birth_date
        ? FormatDateForYearMonthDay(accredited.userInfo.birth_date)
        : "",
      avatarSecureId: accredited.avatar ? accredited.avatar.secure_id : undefined,
      adviceRegister: accredited.userInfo.advice_register,
      type: accredited.type,
      typeOfCare: accredited.userInfo.type_of_care
        ? accredited.userInfo.type_of_care
        : "in_person",
      status: accredited?.userInfo?.status ? accredited?.userInfo?.status : 'active',
      specialtiesSecureIds: accredited.specialties.map((specialty) => ({
        label: specialty.name,
        value: specialty.secure_id,
      })),
      examsSecureIds: accredited.exams.map((exam) => ({
        label: exam.name,
        value: exam.secure_id,
      })),
      paymentMethods: accredited.userInfo.payment_methods
        ? accredited.userInfo.payment_methods.split(",").map((paymentMethod) => ({
          label: paymentMethod,
          value: paymentMethod,
        }))
        : [],
      queryValue: accredited.userInfo.query_value
        ? accredited.userInfo.query_value / 100
        : undefined,
      groupSecureId: accredited.accreditedGroups[0]
        ? { label: accredited.accreditedGroups[0].name, value: accredited.accreditedGroups[0].secure_id }
        : undefined
    },
  });

  const zipCode = watch("zip_code");
  // const watchLegalDocument = (watch("legal_document_number") || "").replace(/\D/g, "");

  useEffect(() => {
    if (zipCode) {
      getCep(zipCode.replace(/[^\d]/g, ''));
    }
  }, [zipCode]);

  const handleSearchGroups = async (search: string) => {
    const { data } = await api.get("v1/admin/list/groups", {
      params: {
        search,
      }
    })

    const groups = data.map((group: any) => ({ label: group.name, value: group.secure_id }))

    return groups
  }

  const edit = useMutation(
    async (values: FormData) => {
      const newCell = values.cell.replace(/\D/g, "");
      const ddd_cell = newCell.slice(0, 2);
      const cell = newCell.slice(2);

      return await api.put(`/v1/admin/accrediteds/${accredited.secure_id}`, {
        ...values,
        ddd_cell,
        cell,
        specialtiesSecureIds: values.specialtiesSecureIds.map(
          (specialty) => specialty.value
        ),
        examsSecureIds: values.examsSecureIds.map(
          (exam) => exam.value
        ),
        paymentMethods: values.paymentMethods
          .map((paymentMethod) => paymentMethod.value)
          .toString(),
        groupSecureId: values.groupSecureId
          ? values.groupSecureId.value
          : null
      });
    },
    {
      onSuccess: (response: AxiosResponse) => {
        queryClient.invalidateQueries(["AccreditedsAdmin"]);
        queryClient.invalidateQueries(["ActionLogsAdmin", accredited.secure_id])
        toast({
          title:
            response.data?.message || "Credenciado alterado com sucesso!",
          position: "top-right",
          status: response.data?.type || "success",
          isClosable: true,
        });
        history.back();
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title:
            error?.response?.data?.message ||
            "Ocorreu um problema ao alterar Credenciado.",
          position: "top-right",
          status: error?.response?.data?.type || "error",
          isClosable: true,
        });
      },
    }
  );

  const handleEdit: SubmitHandler<FormData> = async (values) => {
    try {
      await edit.mutateAsync(values);
    } catch {}
  };

  const isClinic = watch("type");

  const defaultForms = ["lab", "doctor"];
  const isDefaultForm = defaultForms.some((type) => type === isClinic);

  // useEffect(() => {
  //   if (isClinic !== 'lab') {
  //     setValue('examsSecureIds', [])
  //   }
  // }, [isClinic])

  useEffect(() => {
    setValue('cell', watch('cell'))
  }, [])

  // function isCpfOrCnpj() {
  //   if (watchLegalDocument.length === 14) {
  //     return "99.999.999/9999-99"
  //   } else {
  //     return "999.999.999-99999"
  //   }
  // }

  return (
    <VStack spacing="4" layerStyle="container">
      {isDefaultForm && (
        <Box
          p="4"
          as="form"
          width="100%"
          layerStyle="card"
          onSubmit={handleSubmit(handleEdit)}
        >
          <VStack spacing="4" align="flex-start">
            <Grid
              templateColumns={{
                sm: "repeat(4, 1fr)",
                md: "repeat(8, 1fr)",
                lg: "repeat(10, 1fr)",
                xl: "repeat(12, 1fr)",
                "2xl": "repeat(12, 1fr)",
              }}
              gap={6}
              w="100%"
              alignItems="center"
            >
              <GridItem
                colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}
                rowSpan={{ sm: 8, lg: 2 }}
              >
                <InputImage
                  name="avatarSecureId"
                  label="Avatar"
                  watch={watch}
                  setValue={setValue}
                  clearErrors={clearErrors}
                  maxHeight="200px"
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, "2xl": 8 }}>
                <Input
                  placeholder="Nome *"
                  label="Nome *"
                  error={errors.name}
                  {...register("name")}
                />
              </GridItem>

              <GridItem colSpan={{ sm: 4, md: 8, lg: 6, xl: 8, "2xl": 8 }}>
                <Input
                  placeholder="E-mail *"
                  label="E-mail *"
                  type="email"
                  error={errors.email}
                  {...register("email")}
                />
              </GridItem>

              <GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}>
                <Input
                  label="CPF/CNPJ *"
                  placeholder="CPF/CNPJ *"
                  // mask={isCpfOrCnpj()}
                  error={errors.legal_document_number}
                  {...register("legal_document_number")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 4 }}>
                <InputMask
                  label="Celular *"
                  placeholder="Celular *"
                  mask="(99)99999-9999"
                  error={errors.cell}
                  {...register("cell", { onChange: () => {} })}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 4 }}>
                <InputDate
                  label="Data de Nascimento/Fundação *"
                  placeholder="Data de Nascimento/Fundação *"
                  {...register("birth_date")}
                  error={errors.birth_date}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <Divider />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 3, "2xl": 2 }}>
                <Input
                  placeholder="Cep"
                  label="Cep"
                  error={errors.zip_code}
                  {...register("zip_code")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 7, xl: 9, "2xl": 8 }}>
                <Input
                  placeholder="Endereço"
                  label="Endereço"
                  error={errors.street}
                  {...register("street")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 2, xl: 3, "2xl": 2 }}>
                <Input
                  placeholder="Número"
                  label="Número"
                  error={errors.number}
                  {...register("number")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 8, xl: 9, "2xl": 4 }}>
                <Input
                  placeholder="Complemento"
                  label="Complemento"
                  error={errors.complement}
                  {...register("complement")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 3 }}>
                <Input
                  placeholder="Bairro"
                  label="Bairro"
                  error={errors.neighborhood}
                  {...register("neighborhood")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
                <Input
                  placeholder="Cidade"
                  label="Cidade"
                  error={errors.city}
                  {...register("city")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 2 }}>
                <Input
                  placeholder="Estado"
                  label="Estado"
                  error={errors.state}
                  {...register("state")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <Divider />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 4, xl: 4, "2xl": 4 }}>
                <Input
                  placeholder="Registro *"
                  label="Registro *"
                  error={errors.adviceRegister}
                  {...register("adviceRegister")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
                <InputRadio
                  label="Tipo de atendimento"
                  name="typeOfCare"
                  control={control}
                  options={typesOfCares}
                  error={errors.typeOfCare}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 3 }}>
                <InputRadio
                  label="Tipo"
                  name="type"
                  control={control}
                  options={typesDoctors}
                  error={errors.typeOfCare}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3, xl: 4, "2xl": 1 }}>
              <InputRadio
                label="Status"
                name="status"
                control={control}
                options={typesStatus}
                error={errors.status}
              />
            </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 6.666666, xl: 8, "2xl": 8 }}>
                <InputCreatableSelect
                  name="paymentMethods"
                  label="Métodos de Pagamento *"
                  placeholder="Digite um método de pagamento *"
                  noOptionsMessage={() => ""}
                  error={errors.paymentMethods as any}
                  control={control}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3.333333, xl: 4, "2xl": 4 }}>
                <InputNumberMask
                  setValue={setValue}
                  placeholder="R$ 0,00"
                  prefix="R$ "
                  value={accredited.userInfo.query_value
                    ? accredited.userInfo.query_value / 100
                    : ""}
                  error={errors.queryValue}
                  label="Valor Hellomed"
                  name="queryValue"
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 3.333333, xl: 12, "2xl": 12 }}>
                <InputNumberMask
                  setValue={setValue}
                  placeholder="R$ 0,00"
                  prefix="R$ "
                  value={accredited.userInfo.accredited_value
                    ? accredited.userInfo.accredited_value / 100
                    : ""}
                  error={errors.accreditedValue}
                  label="Valor Credenciado"
                  name="accreditedValue"
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <InputSelectMult
                  name="specialtiesSecureIds"
                  label="Especialidades"
                  placeholder="Especialidades"
                  options={specialtiesOptions}
                  control={control}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <InputSelectMult
                  name="examsSecureIds"
                  label="Exames"
                  placeholder="Exames"
                  options={examsOptions}
                  control={control}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <InputAsyncSelect
                  isMulti={false}
                  isClearable
                  defaultOptions
                  control={control}
                  name="groupSecureId"
                  error={errors.groupSecureId}
                  label="Grupo"
                  placeholder="Procure um grupo"
                  handleSearch={handleSearchGroups}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, "2xl": 12 }}>
                <Divider />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
                <InputPassword
                  label="Senha"
                  placeholder="Senha"
                  error={errors.password}
                  {...register("password")}
                />
              </GridItem>
              <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
                <InputPassword
                  label="Confirmação de Senha"
                  placeholder="Repetir a Senha"
                  error={errors.passwordConfirmation}
                  {...register("passwordConfirmation")}
                />
              </GridItem>
            </Grid>

            <Flex justify="flex-end" w="100%">
              <HStack spacing="4" width="20em">
                <ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
                <ButtonSubmit isLoading={formState.isSubmitting}>
                  Salvar
                </ButtonSubmit>
              </HStack>
            </Flex>
          </VStack>
        </Box>
      )}
      {!isDefaultForm && (
        <AccreditedTabsPanel
          clinicId={accredited.secure_id}
          accredited={accredited}
          watch={watch}
          clearErrors={clearErrors}
          control={control}
          errors={errors}
          formState={formState}
          register={register}
          setValue={setValue}
          specialtiesOptions={specialtiesOptions}
          handleSubmit={handleSubmit}
          handleEdit={handleEdit}
          handleSearchGroups={handleSearchGroups}
        />
      )}
      <ListActionLog
        layerStyle="card"
        changedSecureId={accredited.secure_id}
        type="user"
      />
    </VStack>
  );
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
  async (ctx) => {
    const { id } = ctx.query;

    const api = setupApiClient(ctx);
    const { data } = await api.get(`/v1/admin/list/specialties`);
    const { data: dataExam } = await api.get(`/v1/admin/list/exams`)

    const response = await api.get(`/v1/admin/accrediteds/${id}`);

    const specialtiesOptions = data.map((specialty: any) => ({
      value: specialty.secure_id,
      label: specialty.name,
    }));

    const examsOptions = dataExam.map((exam: any) => ({
      value: exam.secure_id,
      label: exam.name,
    }))

    return {
      props: {
        specialtiesOptions,
        examsOptions,
        accredited: response.data,
      },
    };
  },
  {
    roles: ["MASTER", "ADMIN"],
    permissions: ["accrediteds_edit"],
  }
);

export default DoctorsEdit;
