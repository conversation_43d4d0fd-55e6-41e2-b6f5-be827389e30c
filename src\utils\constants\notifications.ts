export const NOTIFICATIONS_TYPE_LIST = [
  { label: "Push", value: "push" },
  { label: "E-mail", value: "email" },
  { label: "SMS", value: "sms" },
];

export const NOTIFICATIONS_LIST = [
  {
    label: "Recebimento de confirmação de consulta",
    value: "consultConfirmation",
  },
  {
    label: "Recebimento de confirmação de exame",
    value: "examConfirmation",
  },
  {
    label: "Recebimento de opções de consulta",
    value: "consultOptions",
  },
  {
    label: "Recebimento de opções de exame",
    value: "examOptions",
  },
  {
    label: "Solicitação de consulta",
    value: "consultSolicitation",
  },
  {
    label: "Solicitação de exame",
    value: "examSolicitation",
  },
  {
    label: "Lembrete de consulta 3 horas antes do horário agendado",
    value: "consult3h",
  },
  {
    label: "Lembrete de consulta 24 horas antes  do horário agendado",
    value: "consult24h",
  },
  {
    label: "Lembrete de exame 3 horas antes do horário agendado",
    value: "exam3h",
  },
  {
    label: "Lembrete de exame 24 horas antes  do horário agendado",
    value: "exam24h",
  },
  {
    label: "Cancelamento de exame pelo paciente",
    value: "examCancelForPatient",
  },
  {
    label: "Cancelamento de consulta pelo paciente",
    value: "consultCancelForPatient",
  },
  {
    label: "Recebimento de confirmação de cancelamento de exame pelo paciente. Obs: apenas backoffice recebe esse e-mail",
    value: "examCancelForPatientSendBackoffice",
  },
  {
    label: "Recebimento de confirmação de cancelamento de consulta pelo paciente. Obs: apenas backoffice recebe esse e-mail",
    value: "consultCancelForPatientSendBackoffice",
  },
];
