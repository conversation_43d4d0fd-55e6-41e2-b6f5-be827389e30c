import { GetServerSideProps, NextPage } from "next";

import { useAuthContext } from "~/contexts/AuthContext";
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth";

import { ModalChangePassword } from "~/components/global/ModalChangePassword";

interface DoctProps {}

const Doctor: NextPage<DoctProps> = () => {
	const { user } = useAuthContext()

	return (
		<>
			<div />
			{user && user.isFirstAccess && (
				<ModalChangePassword
					isOpen={user.isFirstAccess}
				/>
			)}
		</>
	)
};

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		return {
			props: {},
		};
	},
	{
		roles: ["ACCREDITED"],
	}
);

export default Doctor;
