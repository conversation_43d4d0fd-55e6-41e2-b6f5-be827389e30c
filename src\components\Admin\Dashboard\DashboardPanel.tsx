import {
	Box,
	Flex,
	Grid,
	Grid<PERSON>tem,
	HStack,
	SimpleGrid,
	Spinner,
	Tab,
	TabList,
	TabPanel,
	TabPanels,
	Table,
	TableContainer,
	Tabs,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack,
	useToken,
} from "@chakra-ui/react"
import { FC, useMemo } from "react"

import { useForm } from "react-hook-form"

import { api } from "~/services/apiClient"
import { Option } from "~/utils/Types/Global"
import { useControlFilters } from "~/contexts/ControlFiltersContext"
import { useDashboardAdmin } from "~/hooks/Admin/Dashboard/useDashboardAdmin"

import { Pagination } from "~/components/global/Pagination"
import { InputDate } from "~/components/global/Form/InputDate"
import { CardDashboardAppointment } from "./CardDashboardAppointment"
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect"
import { ButtonExportPDF } from "~/components/global/Buttons/ButtonExportPDF"
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel"
import { useScheduleAdmin } from "~/hooks/Admin/Schedule/useScheduleAdmin"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { InputSearch } from "~/components/global/Form/InputSearch"
import { useCan } from "~/hooks/useCan"
import { CardScheduleAdmin } from "../Schedule/CardScheduleAdmin"
import { CardScheduleAdminForDashboard } from "../Schedule/CardScheduleAdminForDashboard"

type FormData = {
	startDate: string
	endDate: string
	specialty: Option
	exam: Option
	partner: Option

	followUpDateStart: string
	followUpDateEnd: string
}

interface DashboardPanelProps {

}

export const DashboardPanel: FC<DashboardPanelProps> = () => {
	const [green100, red100, yellow100] = useToken(
		'colors',
		['green.100', 'red.100', 'yellow.100'],
	)
	const { page, limit, setPage, setLimit } = useControlFilters()
	const { 
		page: schedulePage, 
		limit: scheduleLimit, 
		setPage: setSchedulePage, 
		setLimit: setScheduleLimit,
		setSearch: setScheduleSearch,
		search: scheduleSearch,
	} = useControlFilters()

	const { register, control, watch } = useForm<FormData>({
		defaultValues: {
		}
	})

	const startDate = watch('startDate')
	const endDate = watch('endDate')
	const specialty = watch('specialty')
	const exam = watch('exam')
	const partner = watch('partner')
	
	const watchFollowUpDateStart = watch('followUpDateStart');
	const watchFollowUpDateEnd = watch('followUpDateEnd');

	const { data, error, isLoading, isFetching } = useDashboardAdmin({
		page,
		limit,
		startDate,
		endDate,
		specialty: specialty ? String(specialty.value) : undefined,
		exam: exam ? String(exam.value) : undefined,
		partner: partner ? String(partner.value) : undefined,
		followUpDateStart: watchFollowUpDateStart,
		followUpDateEnd: watchFollowUpDateEnd,

		inCredentialedSchedulesPage: schedulePage,
		inCredentialedSchedulesLimit: scheduleLimit,
		inCredentialedSchedulesName: scheduleSearch,
	});

	const handleSearchSpecialties = async (search: string) => {
		const { data } = await api.get('v1/admin/list/specialties', {
			params: {
				search,
			}
		})

		const partners = data.map((specialty: any) => ({ label: specialty.name, value: specialty.secure_id }))

		return partners
	}

	const handleSearchExams = async (search: string) => {
		const { data } = await api.get('v1/admin/list/exams', {
			params: {
				search,
			}
		})

		const partners = data.map((exam: any) => ({ label: exam.name, value: exam.secure_id }))

		return partners
	}

	const handleSearchPartners = async (search: string) => {
		const { data } = await api.get('v1/admin/list/partners', {
			params: {
				search,
			}
		})

		const partners = data.map((user: any) => ({ label: user.userInfo.name, value: user.secure_id }))

		return partners
	}

	const dataExportAppointmentsAmount = useMemo(() => {
		if (data) {
			return [{
				'Quantidade de atendimentos': data.appointments.meta.total,
			}]
		}
		return [{}]
	}, [data])

	const userCanEdit = useCan({
		permissions: ['schedule_edit']
	});

	const dataExportAppointments = useMemo(() => {
		if (data) {
			const headerObject = data.appointments.data.map(appointment => ({
				'Nome do Paciente': appointment.patient,
				'Seguradora': appointment.partner,
				'Status': appointment.status,
				'Tempo de Expiração': appointment.expiration_time.message,
				'rowColor': appointment.expiration_time.type === 'ok'
					? green100
					: appointment.expiration_time.type === 'warning'
						? yellow100
						: red100
			}))

			return headerObject
		}
		return [{}]
	}, [data]);

	return (
		<VStack spacing="4" layerStyle="container">
			<VStack layerStyle="card" width="100%" p="4">
				<SimpleGrid w="100%" minChildWidth="180px" gap="4">
					<InputDate
						label="Data de"
						placeholder="Data de"
						{...register("startDate")}
					/>
					<InputDate
						label="Até"
						placeholder="Até"
						{...register("endDate")}
					/>
				</SimpleGrid>
				
				<SimpleGrid w="100%" minChildWidth="180px" gap="4">
					<InputDate
						label="Data Retorno de"
						placeholder="Data Retorno de"
						{...register("followUpDateStart")}
					/>
					<InputDate
						label="Data Retorno até"
						placeholder="Data Retorno até"
						{...register("followUpDateEnd")}
					/>
				</SimpleGrid>

				<SimpleGrid w="100%" minChildWidth="180px" gap="4">
					<InputAsyncSelect
						isMulti={false}
						isClearable
						name="specialty"
						label="Especialidade"
						placeholder="Procure uma especialidade"
						control={control}
						handleSearch={handleSearchSpecialties}
					/>
					<InputAsyncSelect
						isMulti={false}
						isClearable
						name="exam"
						label="Exame"
						placeholder="Procure um exame"
						control={control}
						handleSearch={handleSearchExams}
					/>
					<InputAsyncSelect
						isMulti={false}
						isClearable
						name="partner"
						label="Parceiro"
						placeholder="Procure um parceiro"
						control={control}
						handleSearch={handleSearchPartners}
					/>
				</SimpleGrid>
			</VStack>

			<Box p="4" width="100%" layerStyle="card">
				<Tabs variant="enclosed" size="lg" isFitted flex="1">
					<TabList>
						<Tab textStyle="headerSM">Quantidade de atendimentos</Tab>
						<Tab textStyle="headerSM">Tempo médio de atendimento</Tab>
 						<Tab textStyle="headerSM">Em credenciamento</Tab>
					</TabList>

					<TabPanels layerStyle="tabContainer">
						{/* Quantidade de atendimentos */}
						<TabPanel>
							<HStack w="100%" justify="flex-end" mb="6">
								<ButtonExportExcel
									isDisabled={dataExportAppointmentsAmount.length === 0}
									data={dataExportAppointmentsAmount}
									fileName={`quantidade_de_atendimentos`}
								>
									Exportar Excel
								</ButtonExportExcel>
								<ButtonExportPDF
									isDisabled={dataExportAppointmentsAmount.length === 0}
									data={dataExportAppointmentsAmount}
									fileName={`quantidade_de_atendimentos`}
								>
									Exportar PDF
								</ButtonExportPDF>
							</HStack>
							{data && (
								<Grid templateColumns="repeat(4, 1fr)" w="100%" gap="4">
									<GridItem>
										<Text textStyle="headerMD">Quantidade de atendimentos</Text>
										<Box p="4" layerStyle="card" bg="blackAlpha.200">
											<Text textStyle="headerXL">{data && data.appointments.meta.total}</Text>
										</Box>
									</GridItem>
								</Grid>
							)}
						</TabPanel>

						{/* Tempo médio de atendimento */}
						<TabPanel>
							{data && (
								<>
									<HStack w="100%" justify="flex-end" mb="6">
										<ButtonExportExcel
											isDisabled={dataExportAppointments.length === 0}
											data={dataExportAppointments}
											fileName={`Tempo_medio_de_atendimento`}
										>
											Exportar Excel
										</ButtonExportExcel>
										<ButtonExportPDF
											isDisabled={dataExportAppointments.length === 0}
											data={dataExportAppointments}
											fileName={`Tempo_medio_de_atendimento`}
										>
											Exportar PDF
										</ButtonExportPDF>
									</HStack>
									<TableContainer w="100%">
										<Table>
											<Thead>
												<Tr>
													<Th>Nome do Paciente</Th>
													<Th>Seguradora</Th>
													<Th>Status</Th>
													<Th>Tempo de Expiração</Th>
												</Tr>
											</Thead>
											<Tbody>
												{data.appointments.data.map(appointment => (
													<CardDashboardAppointment
														key={appointment.secure_id}
														appointment={appointment}
													/>
												))}
											</Tbody>
										</Table>
									</TableContainer>
									<Flex justify="flex-end" w="100%">
										<Pagination
											totalCountOfRegisters={data.appointments.meta.total}
											registersInCurrentPage={data.appointments.data.length}
											currentPage={data.appointments.meta.page}
											registersPerPage={data.appointments.meta.perPage}
											onPageChange={setPage}
											limit={limit}
											setLimit={setLimit}
										/>
									</Flex>
								</>
							)}
						</TabPanel>
						
						{/* Em credenciamento */}
						<TabPanel>
							<VStack layerStyle="card" width="100%" p="4">
								<Flex w="100%" justify="space-between">
									<Flex>
										{!!error && (
											<Flex justify="center">
												<Text>Falha ao obter dados.</Text>
											</Flex>
										)}
									</Flex>
									<Flex justify="flex-end" align="center" w="100%">
										<HStack spacing="4" alignSelf='flex-end'>
											{isFetching && !isLoading && (
												<Spinner />
											)}
											<Box w="72">
												<InputSearch
													name="search"
													placeholder="Nome"
													setPage={setSchedulePage}
													setSearch={setScheduleSearch}
												/>
											</Box>
										</HStack>

									</Flex>
								</Flex>

									{data && (
										<>
											<TableContainer w="100%">
												<Table whiteSpace="pre-wrap">
													<Thead>
														<Tr>
															<Th>Paciente</Th>
															<Th>Tipo</Th>
															<Th>Especialidade/Exame</Th>
															<Th>
																Data solicitação
															</Th>
															<Th justifyContent="center">
																Data Retorno
															</Th>
															{/* {userCanEdit && (
																<Th>
																	<Text
																		align="center"
																	>
																		Ações
																	</Text>
																</Th>
															)} */}
														</Tr>
													</Thead>

													<Tbody>
														{data.inCredentialSchedules.data.map(schedule => (
															<CardScheduleAdminForDashboard
																key={schedule.secure_id}
																secure_id={schedule.secure_id}
																patient={schedule.patient.userInfo.name}
																type={schedule.type_consult}
																specialtyOrExam={schedule.specialty ? schedule.specialty.name : schedule.exam!.name}
																createdAt={schedule.created_at}
																followUpDate={schedule.followUpDate}
															/>
														))}
													</Tbody>
												</Table>
											</TableContainer>
											<Flex justify="flex-end" w="100%">
												<Pagination
													totalCountOfRegisters={data.inCredentialSchedules.meta.total}
													registersInCurrentPage={data.inCredentialSchedules.data.length}
													currentPage={data.inCredentialSchedules.meta.page}
													registersPerPage={data.inCredentialSchedules.meta.perPage}
													onPageChange={setSchedulePage}
													limit={scheduleLimit}
													setLimit={setScheduleLimit}
												/>
											</Flex>
										</>
									)}
							</VStack>
						</TabPanel>
					</TabPanels>
				</Tabs>
			</Box>
		</VStack>
	)
}
