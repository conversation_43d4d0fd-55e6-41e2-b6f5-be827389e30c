import {
	FormControl,
	FormErrorMessage,
	FormLabel, Icon, IconButton, Input as ChakraInput,
	InputGroup,
	InputProps as ChakraInputProps,
	InputRightElement,
} from '@chakra-ui/react'
import { FieldError } from 'react-hook-form'

import { forwardRef, ForwardRefRenderFunction, useState } from 'react'
import { RiEyeLine, RiEyeOffLine } from 'react-icons/ri'

interface InputProps extends ChakraInputProps {
	name: string
	label?: string
	placeholder?: string
	error?: FieldError
}

const InputPasswordBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = ({ name, label, error = null, ...rest }, ref) => {
	const [show, setShow] = useState(false)
	const handleClick = () => setShow(!show)

	return (
		<FormControl isInvalid={!!error}>
			{!!label &&
				<FormLabel
					w="100%"
					htmlFor={name}
					display="flex"
					alignItems="center"
					fontWeight="bold"
				>
					{label}
				</FormLabel>
			}
			<InputGroup size="lg">
				<ChakraInput
					fontSize={{ sm: 'md', md: 'lg' }}
					id={name}
					name={name}
					autoComplete='new-password'
					size="lg"
					variant="outline"
					ref={ref}
					type={show ? 'text' : 'password'}
					{...rest}
				/>
				<InputRightElement width='4.5rem'>
					{show ? (
						<IconButton
							h='1.75rem'
							size='sm'
							aria-label='Hide password'
							icon={<Icon
								as={RiEyeOffLine}
							/>}
							onClick={handleClick}
						/>
					) : (
						<IconButton
							h='1.75rem'
							size='sm'
							aria-label='Show password'
							icon={<Icon
								as={RiEyeLine}
							/>}
							onClick={handleClick}
						/>
					)}
				</InputRightElement>
			</InputGroup>
			{error &&
				<FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
			}
		</FormControl>
	)
}

export const InputPassword = forwardRef(InputPasswordBase)
