import {
	FormControl,
	FormErrorMessage,
	FormLabel,
	Textarea as ChakraInput,
	TextareaProps as ChakraInputProps,
} from '@chakra-ui/react'
import { FieldError } from 'react-hook-form'

import { forwardRef, ForwardRefRenderFunction } from 'react'

interface InputProps extends ChakraInputProps {
	name: string
	label?: string
	placeholder?: string
	error?: FieldError
}

const InputBase: ForwardRefRenderFunction<HTMLTextAreaElement, InputProps> = ({ name, label, error = null, ...rest }, ref) => {
	return (
		<FormControl isInvalid={!!error}>
			{!!label &&
				<FormLabel
					w="100%"
					htmlFor={name}
					display="flex"
					alignItems="center"
					fontWeight="bold"
				>
					{label}
				</FormLabel>
			}
			<ChakraInput
				fontSize={{ sm: 'md', md: 'lg' }}
				id={name}
				name={name}
				size="lg"
				variant="outline"
				minH="120px"
				ref={ref}
				{...rest}
			/>
			{error &&
				<FormErrorMessage fontSize="xs">{error.message}</FormErrorMessage>
			}
		</FormControl>
	)
}

export const InputTextarea = forwardRef(InputBase)
