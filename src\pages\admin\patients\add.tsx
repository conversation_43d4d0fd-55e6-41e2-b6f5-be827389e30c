import {
	VStack,
	useToast,
	Box,
	Flex,
	HStack,
	Grid,
	GridItem,
	Divider,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { SubmitHand<PERSON>, useForm } from "react-hook-form"

import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { Option } from "~/utils/Types/Global"
import { queryClient } from "~/services/queryClient"
import { UserFormData } from "~/utils/Types/Admin/User"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { FormUser } from "~/components/global/FormUser"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { InputPassword } from "~/components/global/Form/InputPassword"
import { Input } from "~/components/global/Form/Input"
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { cpfValidation } from "~/utils/Validator/validateCpf"

type FormData = UserFormData & {
	origin: string
	partnerSecureId: Option

	isActive: 'true' | 'false'
}

const FormSchema = yup.object().shape({
	userExists: yup.boolean(),
	name: yup.string().required("Nome obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	password: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema
			.required("Senha obrigatória")
			.min(6, "No mínimo 6 caracteres")
	}),
	passwordConfirmation: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.oneOf([yup.ref("password")], "As senha precisam ser iguais")
	}),
	legal_document_number: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("CPF obrigatório")
	})
	.test("cpf-validator", "CPF inválido", (value) => {
    if (!value) return false;

    const cleanedValue = value.replace(/\D/g, "");

    return cpfValidation(cleanedValue);
  }),
	cell: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("Celular obrigatório")
	}),
	birth_date: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("Data de nascimento obrigatória")
	}),
	isActive: yup.mixed<'true' | 'false'>().oneOf(['true', 'false']),

})

interface PatientsAddProps {}

const PatientsAdd: NextPage<PatientsAddProps> = () => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		watch,
		setValue,
		clearErrors,
		control
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			userExists: false,
			isActive: 'true'
		},
	})

	const handleSearchPartners = async (search: string) => {
		const { data } = await api.get('v1/admin/list/partners', {
			params: {
				search,
			}
		})

		const partners = data.map((user: any) => ({ label: user.userInfo.name, value: user.secure_id }))

		return partners
	}

	const add = useMutation(
		async (values: FormData) => {
			const newCell = values.cell.replace(/\D/g, '')
			const ddd_cell = newCell.slice(0, 2)
			const cell = newCell.slice(2)

			return await api.post("/v1/admin/patients", {
				...values,
				ddd_cell,
				cell,
				partnerSecureId: values.partnerSecureId ? values.partnerSecureId.value : undefined,
				isActive: values.isActive === 'true' ? true : false
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["PatientsAdmin"])
				toast({
					title:
						response.data?.message || "Novo paciente cadastrado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao cadastrar paciente.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleAdd: SubmitHandler<FormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch {}
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Box
				p="4"
				as="form"
				width="100%"
				layerStyle="card"
				onSubmit={handleSubmit(handleAdd)}
			>
				<VStack spacing="4" align="flex-start">
					<FormUser
						clearErrors={clearErrors}
						formState={formState}
						register={register as any}
						setValue={setValue as any}
						watch={watch as any}
						textUserExists="já possui cadastro na plataforma, para adiciona-lo como paciente clique em cadastrar."
					/>
					<Grid
						templateColumns={{
							sm: 'repeat(4, 1fr)',
							md: 'repeat(8, 1fr)',
							lg: 'repeat(10, 1fr)',
							xl: 'repeat(12, 1fr)',
							'2xl': 'repeat(12, 1fr)',
						}}
						gap={6}
						w="100%"
					>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
							<Divider />
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
							<Input
								placeholder="Origem"
								label="Origem"
								error={errors.origin}
								{...register("origin")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
							<InputAsyncSelect
								isMulti={false}
								isClearable
								name="partnerSecureId"
								label="Parceiro"
								placeholder="Procure um parceiro"
								control={control}
								error={errors.partnerSecureId}
								handleSearch={handleSearchPartners}
							/>
						</GridItem>
					</Grid>

					<Grid
						templateColumns={{
							sm: 'repeat(4, 1fr)',
							md: 'repeat(8, 1fr)',
							lg: 'repeat(10, 1fr)',
							xl: 'repeat(12, 1fr)',
							'2xl': 'repeat(12, 1fr)',
						}}
						gap={6}
						w="100%"
					>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
							<Divider />
						</GridItem>

						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 5, '2xl': 5 }}>
							<InputPassword
								label="Senha *"
								placeholder="Senha *"
								error={errors.password}
								{...register("password")}
							/>
						</GridItem>

						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 5, '2xl': 5 }}>
							<InputPassword
								label="Confirmação de Senha *"
								placeholder="Repetir a Senha *"
								error={errors.passwordConfirmation}
								{...register("passwordConfirmation")}
							/>
						</GridItem>

						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 2, '2xl': 2 }}>
							<InputSelect
								label="Status"
								options={[
									{ label: "Ativo", value: "true" },
									{ label: "Inativo", value: "false" },
								]}
								// isDisabled={isDisabledChangeStatus}
								{...register("isActive")}
							/>
						</GridItem>
					</Grid>

					<Flex justify="flex-end" w="100%">
						<HStack spacing="4" width="20em">
							<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
							<ButtonSubmit isLoading={formState.isSubmitting}>
								Cadastrar
							</ButtonSubmit>
						</HStack>
					</Flex>
				</VStack>
			</Box>
		</VStack>
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		return {
			props: {},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["patients_create"],
	}
)

export default PatientsAdd
