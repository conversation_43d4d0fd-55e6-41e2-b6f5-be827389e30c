import {
	Flex,
	HStack,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { ListUserAdmin } from "~/utils/Types/Admin/User"
import { ButtonDelete } from "~/components/global/Buttons/ButtonDelete"

type CardPatientPartnerProps = {
	patient: ListUserAdmin;
}

export const CardPatientPartner: FC<CardPatientPartnerProps> = ({ patient }) => {
	const toast = useToast()

	const unlinkPacient = useMutation(async () => {
		return await api.delete(`/v1/partner/patients-partner/${patient.secure_id}`)
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['PatientsPartner'])
			toast({
				title: response.data?.message || 'Paciente desvinculado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao desvincular paciente.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{patient.name}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patient?.partner?.name ? patient?.partner?.name : '-'}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patient.legal_document_number}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patient.cell}</Text>
			</Td>

				<Td>
					<Flex justify="flex-end">
						<HStack>
								<ButtonToEditItemList
									tooltipLabel="Editar paciente"
									linkHref={`/partner/patients/${patient.secure_id}`}
								/>

									<ButtonDelete
										deleteFunction={unlinkPacient}
										titlePopover="Desvincular paciente"
										tooltipLabel="Desvincular paciente"
										message="Deseja desvincular esse paciente?"
									/>
						</HStack>
					</Flex>
				</Td>
		</Tr>
	)
}
