import { api } from "~/services/apiClient";
import { ListAppointmentAdmin } from "~/utils/Types/Admin/Appointment";
import { ListScheduleAdmin } from "~/utils/Types/Admin/Schedule";
import { ListUserAdmin } from "~/utils/Types/Admin/User";

type GetSchedulesAndAppointmentsReportApiResponseDto = {
  meta: Meta;
  data: ScheduleData[];
};

type GetSchedulesAndAppointmentsReportResponse = {
  meta: Meta;
  data: ScheduleData[];
};

type Meta = {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  first_page: number;
  first_page_url: string | null;
  last_page_url: string | null;
  next_page_url: string | null;
  previous_page_url: string | null;
};

type ScheduleData = {
  secure_id: string;
  status: ListScheduleAdmin["status"];
  has_been_accredited: boolean;
  created_at: string;
  appointment: Appointment | null;
  exam: SpecialtyOrExamData | null;
  specialty: SpecialtyOrExamData | null;
  observations: ObservationData[];
  patient: PatientData;
  scheduleDatesRequests: ScheduleDateRequest[];
};

type Appointment = {
  secure_id: string;
  status: ListAppointmentAdmin["status"];
  date: string;
  partner: {
    userInfo: {
      accredited_value: number | null;
    };
  };
};

type SpecialtyOrExamData = {
  secure_id: string;
  name: string;
};

type ObservationData = {
  secure_id: string;
  observation: string;
};

type PatientData = {
  secure_id: string;
  email: string;
  type: ListUserAdmin["type"];
  isActive: 0 | 1;
  partners: Partner[];
  parent: Parent | null;
  userInfo: UserInfo;
};

type UserInfo = {
  legal_document_number: string;
  name: string;
  ddd_cell: number | null;
  cell: number | null;
  birth_date: string | null;
  zip_code: string | null;
  street: string | null;
  number: string | null;
  complement: string | null;
  neighborhood: string | null;
  city: string | null;
  state: string | null;
  origin: string | null;
};

type Parent = {
  secure_id: string;
  userInfo: { legal_document_number: string };
  partners: Partner[];
};

type Partner = {
  userInfo: { name: string };
};

type ScheduleDateRequest = {
  query_value: number;
  query_value_subsidy: number;
  query_value_patient: number;
  date: string;
};

type GetSchedulesAndAppointmentsProps = {
  page: number;
  limit: number;
  targeting?: string;
  specialtySecureId: (string | undefined)[];
  examSecureId: (string | undefined)[];
  stateUFS?: (string | undefined)[];
  citiesNames?: (string | undefined)[];
  openingDate?: string;
  scheduleOrAppointment?: string;
  appointmentStatus: (string | undefined)[];
  scheduleStatus: (string | undefined)[];
};

export async function getAppointmentAndScheduleExport({
  page,
  limit,
  targeting,
  specialtySecureId,
  examSecureId,
  stateUFS,
  citiesNames,
  openingDate,
  scheduleOrAppointment,
  appointmentStatus,
  scheduleStatus,
}: GetSchedulesAndAppointmentsProps): Promise<GetSchedulesAndAppointmentsReportResponse> {
  let currentPage = page;
  let items: ScheduleData[] = [];

  try {
    const fetchPage = async (page: number) => {
      const response = await api.get<GetSchedulesAndAppointmentsReportApiResponseDto>(
        "v1/admin/reports/schedules-and-appointments-export",
        {
          params: {
            page,
            limit,
            consultType: "all",
            targeting: targeting === "both" ? undefined : targeting,
            examOrSpecialtySecureId:
              examSecureId !== undefined ? examSecureId : specialtySecureId,
            stateUFS: stateUFS || undefined,
            citiesNames: citiesNames || undefined,
            openingDate: openingDate || undefined,
            scheduleOrAppointment:
              scheduleOrAppointment === "both" ? undefined : scheduleOrAppointment,
            scheduleOrAppointmentStatus:
              appointmentStatus.length > 0
                ? appointmentStatus
                : scheduleStatus.length > 0
                  ? scheduleStatus
                  : [],
          },
        }
      );

      return response.data;
    };

    const initialResponse = await fetchPage(currentPage);
    items = initialResponse.data;

    const totalPages = initialResponse.meta.last_page;

    while (currentPage < totalPages) {
      currentPage++;
      const paginatedResponse = await fetchPage(currentPage);
      items = [...items, ...paginatedResponse.data];
    }


    return { meta: initialResponse.meta, data: items };
  } catch (error) {
    console.error("Error fetching schedules and appointments:", error);
    throw new Error("Failed to fetch schedules and appointments.");
  }
}
