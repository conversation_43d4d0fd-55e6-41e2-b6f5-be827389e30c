import {
	Flex, Modal,
	ModalBody, ModalContent,
	ModalOverlay, Stack, Text, useToast
} from "@chakra-ui/react"
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { FC } from "react"
import { SubmitHandler, useForm } from 'react-hook-form'
import * as yup from 'yup'
import { api } from "~/services/apiClient"
import { ButtonSubmit } from "../Buttons/ButtonSubmit"
import { useAuthContext } from "~/contexts/AuthContext"
import { InputPassword } from "../Form/InputPassword"

const FormSchema = yup.object().shape({
	password: yup
		.string()
		.required("Senha obrigatória")
		.min(8, "No mínimo 8 caracteres")
		.matches(
			/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/])[A-Za-z\d!@#$%^&*()_\-+=\[\]{};:'",.<>?\\|`~\/]{6,}$/,
			'A senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.'
		),
	passwordConfirmation: yup.string().oneOf([yup.ref("password")], "As senhas precisam ser iguais"),
})

type FormData = {
	password: string
	passwordConfirmation: string | undefined
}

interface ModalChangePasswordProps {
	isOpen: boolean
}

export const ModalChangePassword: FC<ModalChangePasswordProps> = ({ isOpen }) => {
	const { changeFirstAccess } = useAuthContext()

	const toast = useToast()

	const { register, handleSubmit, formState, reset } = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema)
	})

	const { errors } = formState

	const edit = useMutation(async (values: FormData) => {
		return await api.put('/v1/change-password/', values)
	}, {
		onSuccess: (response: AxiosResponse) => {
			toast({
				title: response.data.message || 'Senha alterada com sucesso!',
				position: "top-right",
				status: "success",
				isClosable: true,
			})
			changeFirstAccess()
			reset()
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data.message || 'Ocorreu um erro ao alterar a senha.',
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	})

	const handleEdit: SubmitHandler<FormData> = async (values) => {
		try {
			await edit.mutateAsync(values)
		} catch { }
	}

	return (
		<Modal isOpen={isOpen} onClose={() => { }}>
			<ModalOverlay />
			<ModalContent>
				<ModalBody>
					<Flex
						as="form"
						width="100%"
						p={{ sm: undefined, md: 4 }}
						flexDirection="column"
						onSubmit={handleSubmit(handleEdit)}
					>
						<Stack spacing="6">
							<Text textStyle="headerLG">Altere sua senha</Text>
							<Text textStyle="textSM">Troque a senha para acessar a plataforma. Sua senha deve conter 8 caracteres ou mais, letra maiúscula, letra minúscula, número e caractere especial.</Text>
							<InputPassword
								label="Senha"
								placeholder="Digite a senha"
								{...register('password')}
								error={errors.password}
							/>
							<InputPassword
								label="Confirmação de senha"
								placeholder="Repita a senha"
								{...register('passwordConfirmation')}
								error={errors.passwordConfirmation}
							/>
							<ButtonSubmit
								isLoading={formState.isSubmitting}
								disabled={formState.isSubmitting}
								size="lg"
							>
								Salvar
							</ButtonSubmit>
						</Stack>
					</Flex>
				</ModalBody>
			</ModalContent>
		</Modal>
	)
}
