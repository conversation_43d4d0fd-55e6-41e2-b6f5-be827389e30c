import {
	VStack,
	useToast,
	Box,
	<PERSON>lex,
	HS<PERSON>ck,
	Stack,
	SimpleGrid,
} from "@chakra-ui/react"
import { FC } from "react"

import * as yup from "yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { ListGroupAdmin } from "~/utils/Types/Admin/Group"

import { Input } from "~/components/global/Form/Input"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"

type FormData = {
	name: string
}

const FormSchema = yup.object().shape({
	name: yup.string().required("Nome obrigatório"),
})

interface FormEditGroupProps {
	group: ListGroupAdmin
}

export const FormEditGroup: FC<FormEditGroupProps> = ({ group }) => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
	} = useForm<FormData>({
		resolver: yupResolver(FormSchema),
		defaultValues: {
			name: group.name,
		},
	})

	const edit = useMutation(
		async (values: FormData) => {
			return await api.put(`/v1/admin/groups/${group.secure_id}`, {
				...values,
			})
		},
		{
			onSuccess: (response: AxiosResponse) => {
				queryClient.invalidateQueries(["GroupsAdmin"])
				queryClient.invalidateQueries(["ActionLogsAdmin", group.secure_id])
				toast({
					title:
						response.data?.message || "Grupo alterado com sucesso!",
					position: "top-right",
					status: response.data?.type || "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao alterar o grupo.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleEdit: SubmitHandler<FormData> = async (values) => {
		try {
			await edit.mutateAsync(values)
		} catch {}
	}

	return (
		<Box
			p="4"
			as="form"
			width="100%"
			onSubmit={handleSubmit(handleEdit)}
		>
			<VStack spacing="4" align="flex-start">
				<Stack spacing="6" w="100%">
					<SimpleGrid w="100%" spacing={6} minChildWidth="180px">
						<Input
							placeholder="Nome *"
							label="Nome *"
							error={errors.name}
							{...register("name")}
						/>
					</SimpleGrid>
				</Stack>

				<Flex justify="flex-end" w="100%">
					<HStack spacing="4" width="20em">
						<ButtonCancelSubmit>Cancelar</ButtonCancelSubmit>
						<ButtonSubmit isLoading={formState.isSubmitting}>
							Salvar
						</ButtonSubmit>
					</HStack>
				</Flex>
			</VStack>
		</Box>
	)
}
