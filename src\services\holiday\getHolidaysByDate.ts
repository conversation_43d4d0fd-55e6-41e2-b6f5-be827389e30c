import { format, parse } from "date-fns";
import { api } from "~/services/apiClient";

export async function getHolidaysByDate(currentDate: string) {
  const parseCurrentDate = parse(currentDate, 'dd/MM/yy \'às\' HH:mm', new Date())
	const formattedCurrentDate = format(parseCurrentDate, 'yyyy-MM-dd');
  
  const holidayResponse = await api.get(`/v1/admin/holidays-by-current-date/${formattedCurrentDate}`)


  return holidayResponse.data
}

