import {
  GetServerSideProps,
  GetServerSidePropsContext,
  GetServerSidePropsResult,
} from "next";
import { parseCookies } from "nookies";

import { setupApiClient } from "~/services/api";
import { PERMISSIONS } from "../Types/Permissions";
import { ROLES } from "../Types/Roles";
import { validateUserPermissions } from "./validadeUserPermissions";

type WithSSRAuthOptions = {
  permissions?: PERMISSIONS[];
  roles?: ROLES[];
};

export function WithSSRAuth<P extends { [key: string]: any }>(
  fn: GetServerSideProps<P>,
  options?: WithSSRAuthOptions
) {
  return async (
    ctx: GetServerSidePropsContext
  ): Promise<GetServerSidePropsResult<P>> => {
    const cookies = parseCookies(ctx);

    if (!cookies["@HelloMed:token"]) {
      return {
        redirect: {
          destination: "/login",
          permanent: false,
        },
      };
    }

    if (options) {
      const api = setupApiClient(ctx);
      const response = await api.get("/v1/me");

      const user = {
        name: response.data.user.name,
        email: response.data.user.email,
        isFirstAccess: response.data.user.isFirstAccess,
        roles: response.data.roles,
        permissions: response.data.permissions,
        type: response.data.type
      };

      const { permissions, roles } = options;

      const userHasValidPermissions = validateUserPermissions({
        user,
        permissions,
        roles,
      });

      if (!userHasValidPermissions) {
        let role = "";
        if (
          user.roles.find((r: any) => r === "ADMIN") ||
          user.roles.find((r: any) => r === "MASTER")
        ) {
          role = "admin";
        } else if (
          user.roles.find((r: any) => r === "DOCTOR") ||
          (user.roles.find((r: any) => r === "ACCREDITED"))
        ) {
          role = "doctor";
        } else if (
          user.roles.find((r: any) => r === "PARTNER")
        ) {
          role = "partner";
        } else {
          role = "";
        }

        return {
          redirect: {
            destination: `/${role}`,
            permanent: false,
          },
        };
      }
    }

    return await fn(ctx);
  };
}
