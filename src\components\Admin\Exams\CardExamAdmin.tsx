import {
	Flex,
	HStack,
	Icon,
	Td,
	Text,
	Tr,
	useToast,
} from "@chakra-ui/react"
import { FC } from "react"

import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { IoCheckmarkSharp, IoCloseSharp } from "react-icons/io5"

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { ButtonToEditItemList } from "~/components/global/Buttons/ButtonToEditItemList"
import { ButtonAlterStatus } from "~/components/global/Buttons/ButtonAlterStatus"

type CardExamAdminProps = {
	secure_id: string
	name: string
	status: boolean
}

export const CardExamAdmin: FC<CardExamAdminProps> = ({ secure_id, name, status }) => {
	const toast = useToast()

	const userCanSeeEdit = useCan({
		permissions: ['exams_edit']
	})

	const changeStatus = useMutation(async () => {
		return await api.put(`/v1/admin/exams/${secure_id}`, {
			active: !status
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['ExamsAdmin'])
			toast({
				title: response.data?.message || 'Status da especialidade alterado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			toast({
				title: error?.response?.data?.message || 'Erro ao alterar status da especialidade.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{name}</Text>
			</Td>
			<Td>
				<Flex
					justifyContent="center"
					alignItems="center"
					width="100%"
					height="100%"
				>
					{!!status ? (
						<Icon
							as={IoCheckmarkSharp}
							fontSize="25"
							color="green"
						/>
					) : (
						<Icon
							as={IoCloseSharp}
							fontSize="25"
							color="red"
						/>
					)}
				</Flex>
			</Td>
			{userCanSeeEdit && (
				<Td>
					<Flex justify="flex-end">
						<HStack>
							<ButtonToEditItemList
								tooltipLabel="Editar especialidade"
								linkHref={`/admin/exams/${secure_id}`}
							/>
							<ButtonAlterStatus
								changeStatus={changeStatus}
								titlePopover="Alterar status"
								tooltipLabel="Alterar status"
								message="Deseja Alterar status?"
								status={status}
							/>
						</HStack>
					</Flex>
				</Td>
			)}
		</Tr>
	)
}
