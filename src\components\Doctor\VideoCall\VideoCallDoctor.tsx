import { <PERSON>, Flex, VStack } from "@chakra-ui/react";
import React, { CSSProperties, useEffect, useMemo, useState } from "react";
import { IoCallOutline } from "react-icons/io5";
import { BiMicrophone, BiMicrophoneOff } from "react-icons/bi";
import { BsCameraVideo, BsCameraVideoOff, BsPersonFill } from "react-icons/bs";
import FloatButton from "./FloatButton";
import Participant from "./Participant";
import { LocalParticipant, RemoteParticipant, Room } from "twilio-video";
interface VideoCallDoctor {
  handleLogout: () => void;
  participant?: LocalParticipant;
  isLoading?: boolean;
  participants?: RemoteParticipant[];
}

export default function VideoCallDoctor({
  handleLogout,
  participant,
  isLoading,
  participants,
}: VideoCallDoctor) {
  const buttonStyles: CSSProperties = {
    position: "absolute",
    left: 8,
    top: 8,
  };

  const buttonProps = {
    size: 24,
    color: "#fff",
    style: buttonStyles,
  };

  const [muteAudio, setMuteAudio] = useState(false);
  const [muteVideo, setMuteVideo] = useState(false);
  const [participantMuteAudio, setParticipantMuteAudio] = useState(false);
  const [participantMuteVideo, setParticipantMuteVideo] = useState(false);

  const remoteParticipants = useMemo(() => {
    if (participants?.length && !isLoading) {
      const remoteParticipant = participants[participants?.length - 1];
      return (
        <Participant
          key={remoteParticipant?.sid}
          participant={remoteParticipant}
        />
      );
    }
    return null;
  }, [participants, isLoading]);

  const handleMuteAudio = () => {
    setMuteAudio((prev) => !prev);

    if (!muteAudio) {
      participant?.audioTracks.forEach((audioTrack) => {
        audioTrack.track.disable();
      });
      setMuteAudio(true);
    } else {
      participant?.audioTracks.forEach((audioTrack) => {
        audioTrack.track.enable();
      });
      setMuteAudio(false);
    }
  };
  const handleMuteVideo = () => {
    setMuteVideo((prev) => !prev);
    if (!muteVideo) {
      participant?.videoTracks.forEach((videoTrack) => {
        // videoTrack.unpublish();
        videoTrack.track.disable();
        // videoTrack.track.stop();
      });
      setMuteVideo(true);
    } else {
      // createLocalVideoTrack().then(function (localTrack) {
      //   return participant?.publishTrack(localTrack);
      // });
      participant?.videoTracks.forEach((videoTrack) => {
        videoTrack.track.restart();
        videoTrack.track.enable();
        // videoTrack.track.attach();
      });

      setMuteVideo(false);
    }
  };

  return (
    <VStack w="100%" layerStyle="container">
      <Box width="100%" layerStyle="card" height="500px" pos="relative">
        <Flex
          w="100%"
          height="100%"
          bg="#000"
          rounded="6px"
          align="center"
          justify="center"
        >
          {remoteParticipants}
          {!remoteParticipants && !isLoading && !participantMuteVideo && (
            <BsPersonFill color="#fff" size={122} />
          )}
          {(participantMuteVideo || participantMuteAudio) && (
            <Flex pos="absolute" left={2} top={2} zIndex={100} gap={4}>
              {participantMuteVideo && (
                <BsCameraVideoOff color="#fff" size={16} />
              )}
              {participantMuteAudio && (
                <BiMicrophoneOff color="#fff" size={16} />
              )}
            </Flex>
          )}
          {!isLoading && (
            <Flex
              w="100%"
              rounded="6px"
              align="center"
              justify="center"
              pos="absolute"
              bottom={10}
              left={0}
              gap={4}
            >
              <FloatButton
                mainIcon={<BiMicrophone {...buttonProps} />}
                flipIcon={<BiMicrophoneOff {...buttonProps} />}
                onClick={handleMuteAudio}
                title={muteAudio ? "Ligar microfone" : "Desligar microfone"}
              />
              <FloatButton
                mainIcon={<BsCameraVideo {...buttonProps} />}
                flipIcon={<BsCameraVideoOff {...buttonProps} />}
                onClick={handleMuteVideo}
                title={muteVideo ? "Ligar câmera" : "Desligar câmera"}
              />
              <FloatButton
                color="#F00"
                opacity={1}
                mainIcon={<IoCallOutline {...buttonProps} />}
                onClick={handleLogout}
                title="Finalizar chamada"
              />
            </Flex>
          )}
        </Flex>

        {!isLoading && (
          <Flex
            width="20%"
            height="25%"
            bg="#000"
            pos="absolute"
            top={4}
            right={4}
            border="1px solid #656565"
            rounded="6px"
          >
            <Participant
              participant={participant}
              isLoading={isLoading}
              rounded="6px"
            />
            {muteVideo && !isLoading && (
              <Flex pos="absolute" top="2.5rem" left="90px">
                <BsPersonFill color="#fff" size={32} />
              </Flex>
            )}
            {(muteVideo || muteAudio) && (
              <Flex pos="absolute" right={2} top={2} zIndex={100} gap={4}>
                {muteVideo && <BsCameraVideoOff color="#fff" size={16} />}
                {muteAudio && <BiMicrophoneOff color="#fff" size={16} />}
              </Flex>
            )}
          </Flex>
        )}
      </Box>
    </VStack>
  );
}
