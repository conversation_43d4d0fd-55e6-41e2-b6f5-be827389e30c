import {
	Button,
	Checkbox,
	Divider,
	Flex,
	Grid,
	GridItem,
	HStack,
	Image,
	Link,
	Stack,
	Table,
	TableContainer,
	Tbody,
	Td,
	Text,
	Th,
	Thead,
	Tr,
	useToast,
	VStack
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"
import { useCallback, useState } from "react"

import { setupApiClient } from "~/services/api"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"
import { ScheduleDatesRequestsProps, ScheduleShowProps } from "~/utils/Types/Admin/Schedule"
import { FormatDateForDayMonthYearUsingBars, FormatDateForHourMinutes } from "~/utils/Functions/FormatDates"

import { ListActionLog } from "~/components/Admin/ActionLog/ListActionLog"
import { useMutation } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"
import { AxiosError } from "axios"

type FormData = {
	status: "approved"
	scheduleDateRequestSecureId: string
}

interface ScheduleEditProps {
	schedule: ScheduleShowProps
}

const ScheduleEdit: NextPage<ScheduleEditProps> = ({ schedule }) => {
	const toast = useToast()
	const [selectedDate, setSelectedDate] = useState<ScheduleDatesRequestsProps>()

	const checkDateType = useCallback((data: ScheduleDatesRequestsProps) => {
		if (data.date_type === 'period') {
			switch (data.value) {
				case 'morning':
					return 'Manhã'

				case 'afternoon':
					return 'Tarde'

				case 'night':
					return 'Noite'
				default:
					return ''
			}
		}
		return FormatDateForHourMinutes(data.date)
	}, [])

	const checkStatus = useCallback((data: ScheduleDatesRequestsProps) => {
		switch (data.status) {
			case 'to_check':
				return { text: 'Checar', color: 'orange.400' }

			case 'unavailable':
				return { text: 'Indisponível', color: 'red' }

			case 'available':
				return { text: 'Disponível', color: 'green' }
			default:
				return { text: '', color: '' }
		}
	}, [])

	const handleSchedule = useMutation(
		async (data: FormData) => {
			return api.put(`/v1/admin/schedules/${schedule.secure_id}`, data)
		},
		{
			onSuccess: () => {
				queryClient.invalidateQueries(["schedules"])
				queryClient.invalidateQueries(["ScheduleAdmin"])
				toast({
					title: "Solicitação de agendamento atualizada com sucesso.",
					position: "top-right",
					status: "success",
					isClosable: true,
				})
				history.back()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Erro ao alterar a solicitação de agendamento.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleApprove = async () => {
		try {
			if (selectedDate?.status !== "available") {
				toast({
					title: "Selecione um horário disponível.",
					position: "top-right",
					status: "error",
					isClosable: true,
				})
				return
			}
			await handleSchedule.mutateAsync({ status: "approved", scheduleDateRequestSecureId: selectedDate.secure_id })
		} catch {}
	}

	return (
		<VStack spacing="4" layerStyle="container">
			<Stack
				w="100%" background="white" rounded="md" shadow="md" padding={4}
			>
				<Grid templateColumns={{
					sm: 'repeat(4, 1fr)',
					md: 'repeat(8, 1fr)',
					lg: 'repeat(10, 1fr)',
					xl: 'repeat(12, 1fr)',
					'2xl': 'repeat(12, 1fr)',
				}} gap={6}>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }} >
						<Text
							fontSize="xs"
						>
							Solicitação
						</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">{schedule.type_consult !== 'exam' ? 'Consulta com:' : 'Exame:'}</Text>
							<Text>{schedule.specialty?.name}{schedule.exam?.name}</Text>
						</HStack>
					</GridItem>
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }} >
						<Text
							fontSize="xs"
						>
							Paciente
						</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">Nome: </Text>
							<Text>{schedule.patient.userInfo.name}</Text>
						</HStack>
						{schedule.patient.userInfo.phone && <HStack>
							<Text fontSize="sm" fontWeight="medium">Telefone: </Text>
							<Text>({schedule.patient.userInfo.ddd_phone}) - {schedule.patient.userInfo.phone}</Text>
						</HStack>}
						{schedule.patient.userInfo.cell && <HStack>
							<Text fontSize="sm" fontWeight="medium">Celular: </Text>
							<Text>({schedule.patient.userInfo.ddd_cell}) - {schedule.patient.userInfo.cell}</Text>
						</HStack>}
					</GridItem>
					{schedule.patient?.parent && <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }} >
						<Stack>
							<Text
								fontSize="xs"
							>
								Responsável
							</Text>
							<Divider />
							<HStack>
								<Text fontSize="sm" fontWeight="medium">Nome: </Text>
								<Text>{schedule.patient?.parent.userInfo.name}</Text>
							</HStack>
							{schedule.patient?.parent.userInfo.phone && <HStack>
								<Text fontSize="sm" fontWeight="medium">Telefone: </Text>
								<Text>({schedule.patient?.parent.userInfo.ddd_phone}) - {schedule.patient?.parent.userInfo.phone}</Text>
							</HStack>}
							{schedule.patient?.parent.userInfo.cell && <HStack>
								<Text fontSize="sm" fontWeight="medium">Celular: </Text>
								<Text>({schedule.patient?.parent.userInfo.ddd_cell}) - {schedule.patient?.parent.userInfo.cell}</Text>
							</HStack>}
						</Stack>
					</GridItem>}
					<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }} >
						<Text fontSize="xs">Local</Text>
						<Divider />
						<HStack>
							<Text fontSize="sm" fontWeight="medium">Bairro: </Text>
							<Text>{schedule.neighborhood}</Text>
						</HStack>
						<HStack>
							<Text fontSize="sm" fontWeight="medium">Cidade: </Text>
							<Text>{schedule.city}</Text>
						</HStack>
						<HStack>
							<Text fontSize="sm" fontWeight="medium">Estado: </Text>
							<Text>{schedule.state}</Text>
						</HStack>
					</GridItem>
					{schedule.uploads && schedule.uploads?.length > 0 && (
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
							<Text fontSize="xs">Solicitação de Exame</Text>
							<Divider />
							{schedule.uploads.map(item => (
								<Link href={item.url} target="_blank" key={item.url}>
									<Image src={item.url} w={14} h={16} />
								</Link>
							))}

						</GridItem>
					)}
				</Grid>

			</Stack>

			<Stack w="100%" background="white" rounded="md" shadow="md" padding={4}>
				<Flex>
				</Flex>
				<TableContainer>
					<Table variant='simple'>
						<Thead>
							<Tr>
								<Th />
								<Th>Médico / Clinica / Laboratório</Th>
								<Th>Telefone</Th>
								<Th>Data</Th>
								<Th>Horário</Th>
								<Th>Disponibilidade</Th>
							</Tr>
						</Thead>
						<Tbody>
							{schedule.scheduleDatesRequests.map((item, index) => (
								<Tr key={item.secure_id}>
									<Td>
										<Checkbox
											isChecked={item.secure_id === selectedDate?.secure_id}
											isDisabled={item.status !== "available"}
											onChange={() => setSelectedDate(item)}
										/>
									</Td>
									<Td>{item.partner && item.partner.userInfo ? item.partner.userInfo.name : 'helloMed'}</Td>
									<Td>{item.partner && item.partner.userInfo ? ` (${item.partner.userInfo.ddd_cell})${item.partner.userInfo.cell}` : ''}</Td>
									<Td >{FormatDateForDayMonthYearUsingBars(item.date)}</Td>
									<Td>{checkDateType(item)}</Td>
									<Td color={checkStatus(item).color}>{checkStatus(item).text}</Td>
								</Tr>
							))}
						</Tbody>
					</Table>
				</TableContainer>
			</Stack>

			<Stack flexDirection="row" width="100%" justify="flex-end">
				<Button
					colorScheme="facebook"
					onClick={handleApprove}
					isLoading={handleSchedule.isLoading}
					isDisabled={handleSchedule.isLoading || !selectedDate}
				>
					Aprovar
				</Button>
			</Stack>

			<ListActionLog
				layerStyle="card"
				changedSecureId={schedule.secure_id}
				type="schedule"
			/>
		</VStack >
	)
}

export const getServerSideProps: GetServerSideProps = WithSSRAuth(
	async (ctx) => {
		const { id } = ctx.query

		const api = setupApiClient(ctx)

		const { data } = await api.get(`/v1/admin/schedules/${id}`)

		if (data.status === "waiting_backoffice" || data.status === "budget") {
			return {
				redirect: {
					destination: `/admin/schedules/${id}`,
					permanent: false
				}
			}
		} else if (data.status !== "waiting_patient") {
			return {
				redirect: {
					destination: `/admin/schedules/${id}/view`,
					permanent: false
				}
			}
		}

		return {
			props: {
				schedule: data
			},
		}
	},
	{
		roles: ["MASTER", "ADMIN"],
		permissions: ["schedule_edit"],
	}
)

export default ScheduleEdit
