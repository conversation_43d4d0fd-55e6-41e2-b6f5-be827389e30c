import { useQuery } from "@tanstack/react-query";
import { api } from "~/services/apiClient";

type GetDependencyCityByStateUFProps = {
  stateUFS: (string | undefined)[];
}

type DependenciesGetCityByEstadoUFResponse = {
  cities: { name: string; }[];
}

export type GetDependencyCityByStateUFResponse = {
  cities: { label: string, value: string }[];
}

export async function getDependencyCitiesByStateUFS({ stateUFS }: GetDependencyCityByStateUFProps): Promise<GetDependencyCityByStateUFResponse> {

  const response = await api.get<DependenciesGetCityByEstadoUFResponse>(
    `/v1/admin/reports/schedules-and-appointments-dependencies-get-city/${stateUFS}`
  )

  const allCities: any = [];

  const formattedCitiesAsSelectOptions = response?.data?.cities?.map((city) => {
    return {
      label: city.name,
      value: city.name
    }
  })

  const formattedCities = [
    ...allCities,
    ...formattedCitiesAsSelectOptions
  ]

  return {
    cities: formattedCities
  }
}

export function useSchedulesAndAppointmentsGetCitiesByUFS({ stateUFS }: GetDependencyCityByStateUFProps) {
  return useQuery(['ScheduleAndAppointmentsAdminDependencyGetCitiesByUFS', stateUFS], () => getDependencyCitiesByStateUFS({ stateUFS: stateUFS }),
    {
      enabled: Array.isArray(stateUFS) && stateUFS.length > 0
    })
}
