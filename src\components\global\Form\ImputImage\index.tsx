import {
	Box,
	Button,
	Flex,
	FormControl,
	FormErrorMessage,
	FormLabel,
	Icon,
	Image,
	InputProps as ChakraInputProps,
	useDisclosure
} from '@chakra-ui/react'
import { forwardRef, ForwardRefRenderFunction, useCallback, useEffect, useState } from 'react'
import { FieldError, UseFormClearErrors, UseFormSetValue, UseFormWatch } from 'react-hook-form'
import { RiUploadCloud2Line } from 'react-icons/ri'
import { api } from '~/services/apiClient'
import { ModalManagementImage } from './ModalManagementImage'

interface InputProps extends ChakraInputProps {
	name: string
	label?: string
	error?: FieldError
	setValue: UseFormSetValue<any>
	watch: UseFormWatch<any>
	clearErrors: UseFormClearErrors<any>
	maxHeight?: string
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> = ({
	name,
	setValue,
	error,
	label,
	watch,
	clearErrors,
	maxHeight = "64",
	maxWidth,
}, ref) => {
	const { isOpen, onClose, onOpen } = useDisclosure()

	const [urlImageSelected, setUrlImageSelected] = useState<string>()
	const uploadId = watch(name)

	const urlImageActive = useCallback(async () => {
		const response = await api.get(`/v1/uploads/${uploadId}`)
		setUrlImageSelected(response.data.url)
	}, [uploadId])

	useEffect(() => {
		if (!!uploadId) {
			clearErrors()
			urlImageActive()
		} else {
			setUrlImageSelected(undefined)
		}
	}, [uploadId])

	return (
		<>
			<Box background="blackAlpha.50" rounded="md" h="100%" maxW={maxWidth} maxH={maxHeight} position="relative" w="100%">
				{!!urlImageSelected && (
					<Image src={urlImageSelected} h="100%" w="100%" objectFit="contain" maxH="inherit" />
				)}
				<Flex position="absolute" top="0" width="100%" h="100%" zIndex="1" justify="center" align="center">
					<FormControl
						isInvalid={!!error}
						maxW="20"
					>
						{!!label &&
							<FormLabel
								w="100%"
								htmlFor={name}
								display="flex"
								alignItems="center"
								fontWeight="bold"
								textStyle="textMD"
							>
								{label}
							</FormLabel>
						}
						<Button size="80" colorScheme="gray" padding="5" onClick={onOpen} border={error && '1px dashed red'}>
							<Icon as={RiUploadCloud2Line} fontSize="50" />
						</Button>
						{error &&
							<FormErrorMessage my="0" ml="2" pt="2" fontSize="xs">{error.message}</FormErrorMessage>
						}
					</FormControl>
				</Flex>
			</Box>
			<ModalManagementImage
				isOpen={isOpen}
				onClose={onClose}
				setValue={setValue}
				name={name}
				watch={watch}
			/>
		</>
	)
}

export const InputImage = forwardRef(InputBase)
