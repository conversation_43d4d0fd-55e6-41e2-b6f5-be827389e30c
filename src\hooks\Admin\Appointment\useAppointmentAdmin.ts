import { useQuery } from "@tanstack/react-query"
import { api } from "~/services/apiClient"
import { ListAppointmentAdmin } from "~/utils/Types/Admin/Appointment"

type GetAppointmentResponse = {
	total: number
	page: number
	lastPage: number
	perPage: number
	appointments: ListAppointmentAdmin[]
}

type GetAppointmentProps = {
	page: number
	search?: string
	limit?: number
	field?: string;
	direction?: 'asc' | 'desc';
	status?: string;
	currentStatus?: string;

	stateUF?: string;
	cityName?: string;
	specialtySecureId?: string;
	examSecureId?: string;
}

export async function getAppointmentAdmin({
	page,
	search,
	limit,
	direction,
	field,
	status,
	currentStatus,

	stateUF,
	cityName,
	specialtySecureId,
	examSecureId
}: GetAppointmentProps): Promise<GetAppointmentResponse> {
	const receivedSpecialtySecureId = specialtySecureId &&
		specialtySecureId.toLocaleLowerCase() !== 'selecione uma especialidade/exame' ?
		specialtySecureId :
		undefined;

	const receivedExamSecureId = examSecureId &&
		examSecureId.toLocaleLowerCase() !== 'selecione uma especialidade/exame' ?
		examSecureId :
		undefined;

	const response = await api.get('/v1/admin/appointments', {
		params: {
			search,
			page,
			limit,
			direction,
			field: !!field ? field : undefined,
			status: (!status || status === 'all') ? undefined : [status],
			currentStatus: (!currentStatus || currentStatus === 'all') ? undefined : [currentStatus],

			stateUF: stateUF && stateUF.toLocaleLowerCase() !== 'selecione um estado' ? stateUF : undefined,
			examOrSpecialtySecureId: receivedExamSecureId !== undefined ? receivedExamSecureId : receivedSpecialtySecureId,
			// specialtySecureId: specialtySecureId && 
			// 	specialtySecureId.toLocaleLowerCase() !== 'selecione uma especialidade/exame' ? 
			// 	specialtySecureId :
			// 	undefined,

			// examSecureId: examSecureId && 
			// 	examSecureId.toLocaleLowerCase() !== 'selecione uma especialidade/exame' ? 
			// 	examSecureId :
			// 	undefined,

			cityName: cityName && cityName.toLocaleLowerCase() !== 'selecione uma cidade' ?
				cityName :
				undefined
		}
	})

	return {
		total: response.data.meta.total,
		perPage: response.data.meta.per_page,
		page: response.data.meta.current_page,
		lastPage: response.data.meta.last_page,
		appointments: response.data.data,
	}
}

export function useAppointmentAdmin({
	page,
	search,
	limit,
	direction,
	field,
	status,
	currentStatus,

	stateUF,
	cityName,
	specialtySecureId,
	examSecureId
}: GetAppointmentProps) {
	return useQuery(
		[
			'AppointmentAdmin',
			page,
			search,
			limit,
			direction,
			field,
			status,
			currentStatus,
			stateUF,
			cityName,
			specialtySecureId,
			examSecureId,
		],
		() => getAppointmentAdmin({
			page,
			search,
			limit,
			direction,
			field,
			status,
			currentStatus,

			stateUF,
			cityName,
			specialtySecureId,
			examSecureId
		})
	)
}
