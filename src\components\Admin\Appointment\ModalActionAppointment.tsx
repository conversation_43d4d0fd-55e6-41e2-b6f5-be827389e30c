import {
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	VStack,
} from "@chakra-ui/react"
import { FC } from "react"

import { ListActionLog } from "../ActionLog/ListActionLog"

interface ModalActionAppointmentProps {
	isOpen: boolean
	closeModal: () => void
	
	appointmentSecureId: string
}

export const ModalActionAppointment: FC<ModalActionAppointmentProps> = ({ closeModal, isOpen, appointmentSecureId }) => {

	const handleCloseModal = () => {
		closeModal()
	}

	return (
		<Modal size="2xl" isOpen={isOpen} onClose={handleCloseModal}>
			<ModalOverlay />
			<ModalContent
			>
				<ModalHeader>Histórico</ModalHeader>
				<ModalCloseButton />
				<ModalBody>
					<VStack
						width="100%"
						justify="center"
						align="flex-start"
					>
						<ListActionLog
							changedSecureId={appointmentSecureId}
							type="appointment"
						/>
					</VStack>
				</ModalBody>
			</ModalContent>
		</Modal>
	)
}
