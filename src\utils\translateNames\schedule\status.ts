export const getScheduleStatus = (status: string): string => {
	switch (status) {
		case 'approved':
			return 'Aprovada'
			
		case "waiting_backoffice":
			return "Aguardando Backoffice"

		case "waiting_patient":
			return "Aguardando Paciente"

		case "budget":
			return "Orçamento"

		case "canceled_by_patient":
			return "Cancelado pelo Paciente"

		case "canceled_at_patient_request":
			return "Cancelado a pedido do Paciente"

		case "canceled_by_backoffice":
			return "Cancelado pelo Backoffice"

		case "in_accreditation":
			return "Em credenciamento"

		case "no_contact":
			return "Sem Contato"

		case "no_interest":
			return "Sem Interesse"

		case "lack_request":
			return "Falta Pedido"

		case "info_divergence":
			return "Divergência de Informações"

		case "financial_condition":
			return "Condição Financeira"

		case "no_interest_accreditation":
			return "Sem Interesse Credenciamento"

		default:
			return '-'
	}
}
