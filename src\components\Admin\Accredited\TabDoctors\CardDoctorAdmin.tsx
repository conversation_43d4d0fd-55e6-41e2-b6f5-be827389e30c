import { Flex, HStack, Td, Text, Tr, useToast } from "@chakra-ui/react";
import { FC } from "react";

import { useMutation } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";

import { useCan } from "~/hooks/useCan";
import { api } from "~/services/apiClient";
import { queryClient } from "~/services/queryClient";

import { ButtonDelete } from "~/components/global/Buttons/ButtonDelete";

type CardDoctorAdminProps = {
  secureId: string;
  clinicId: string;
  name: string;
};

export const CardDoctorAdmin: FC<CardDoctorAdminProps> = ({
  clinicId,
  secureId,
  name,
}) => {
  const toast = useToast();

  const userCanSeeDelete = useCan({
    permissions: ["accrediteds_delete"],
  });


  const deleteUser = useMutation(
    async () => {
      return await api.delete(`/v1/admin/accrediteds/${clinicId}/doctors/${secureId}`);
    },
    {
      onSuccess: (response: AxiosResponse) => {
        queryClient.invalidateQueries(["ClinicsAdmin"]);
        queryClient.invalidateQueries(["ActionLogsAdmin"]);
        toast({
          title: response.data?.message || "Médico apagado com sucesso!",
          position: "top-right",
          status: response.data?.type || "success",
          isClosable: true,
        });
      },
      onError: (error: AxiosError<any>) => {
        toast({
          title: error?.response?.data?.message || "Erro ao apagar médico.",
          position: "top-right",
          status: error?.response?.data?.type || "error",
          isClosable: true,
        });
      },
    }
  );

  return (
    <Tr>
      <Td>
        <Text fontSize="sm">{name}</Text>
      </Td>
      {(userCanSeeDelete) && (
        <Td>
          <Flex justify="center">
            <HStack>
              {userCanSeeDelete && (
                <ButtonDelete
                  deleteFunction={deleteUser}
                  titlePopover="Remover médico"
                  tooltipLabel="Remover médico"
                  message="Deseja remover esse médico?"
                />
              )}
            </HStack>
          </Flex>
        </Td>
      )}
    </Tr>
  );
};
