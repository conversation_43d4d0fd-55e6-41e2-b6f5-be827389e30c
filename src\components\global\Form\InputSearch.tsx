import {
	FormControl,
	FormErrorMessage,
	FormLabel,
	Icon,
	Input as ChakraInput,
	InputGroup,
	InputProps as ChakraInputPropsProps,
	InputRightElement
} from '@chakra-ui/react'
import { useRouter } from 'next/router'

import { Dispatch, forwardRef, ForwardRefRenderFunction, SetStateAction, useEffect, useState } from 'react'

import { FieldError } from 'react-hook-form'
import { RiSearchLine } from 'react-icons/ri'

import { useDebouncedPromise } from '~/utils/Functions/useDebouncedPromise'

interface InputProps extends ChakraInputPropsProps {
	name: string
	label?: string
	placeholder?: string
	error?: FieldError
	setSearch: Dispatch<SetStateAction<string | undefined>>
	setPage: Dispatch<SetStateAction<number>>
	search?: string
}

const InputBase: ForwardRefRenderFunction<HTMLInputElement, InputProps> =
	({ name, label, setSearch, setPage, error = null, search, ...rest }, ref) => {
		const router = useRouter()
		const [searchValue, setSearchValue] = useState('')
		const [searchDefault, setSearchDefault] = useState<string>()

		async function HandleSearch() {
			setPage(1)
			setSearch(searchValue.length < 2 ? undefined : searchValue)
			setSearchDefault(searchValue.length < 2 ? undefined : searchValue)
		}

		const debouncedChange = useDebouncedPromise(HandleSearch, 1000)

		function HandleSearchValue(value: string) {
			if (value.length < 2) {
				setSearch(undefined)
				setSearchDefault(undefined)
			}
			setSearchValue(value)
			setSearchDefault(value)
			// setSearchValue(value.replace(/\s/g, '%'))
		}

		useEffect(() => {
			if (searchValue) {
				debouncedChange()
			}
		}, [searchValue])

		useEffect(() => {
			const resultSearchStore = sessionStorage.getItem(`${router.asPath}-search`)
			if (!!resultSearchStore) {
				setSearchDefault(resultSearchStore)
			} else if (!!search) {
				setSearchDefault(undefined)
			}
		}, [])

		return (
			<FormControl isInvalid={!!error} >
				{!!label && <FormLabel color="blue.700" htmlFor={name}>{label}</FormLabel>}
				<InputGroup size="md">
					<ChakraInput
						id={name}
						name={name}
						variant="filled"
						bg="blackAlpha.100"
						size="lg"
						onChange={event => HandleSearchValue(event.target.value)}
						onKeyPress={event => event.key === 'Enter' && HandleSearch()}
						ref={ref}
						defaultValue={searchDefault}
						{...rest}
					/>
					<InputRightElement zIndex="0" width="2.5rem" onClick={HandleSearch} height="100%">
						<Icon as={RiSearchLine} fontSize="22" cursor="pointer" />
					</InputRightElement>
				</InputGroup>
				{!!error && (
					<FormErrorMessage
						color="red.300"
					>
						{error.message}
					</FormErrorMessage>
				)}
			</FormControl>
		)
	}

export const InputSearch = forwardRef(InputBase)
